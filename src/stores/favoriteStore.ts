import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Video } from '../types';
import { useAuthStore } from './authStore';

interface FavoriteState {
  favorites: Video[];
  isLoading: boolean;
  error: string | null;

  // Fetch operations
  fetchFavorites: () => Promise<void>;

  // Favorite operations
  addToFavorites: (videoId: string) => Promise<boolean>;
  removeFromFavorites: (videoId: string) => Promise<boolean>;
  isFavorite: (videoId: string) => boolean;
}

export const useFavoriteStore = create<FavoriteState>((set, get) => ({
  favorites: [],
  isLoading: false,
  error: null,

  fetchFavorites: async () => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ favorites: [], error: null });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      // Get user's favorites
      const { data: favoritesData, error: favoritesError } = await supabase
        .from('favorites')
        .select('video_id')
        .eq('user_id', user.id);

      if (favoritesError) {
        throw new Error(favoritesError.message);
      }

      if (!favoritesData || favoritesData.length === 0) {
        set({ favorites: [], isLoading: false });
        return;
      }

      // Get the video details for each favorite
      const videoIds = favoritesData.map(fav => fav.video_id);

      const { data: videosData, error: videosError } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            id,
            username,
            avatar_url
          )
        `)
        .in('id', videoIds);

      if (videosError) {
        throw new Error(videosError.message);
      }

      // Transform the data to match our Video type
      const favorites: Video[] = videosData.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: item.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
        videoUrl: item.video_url,
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: item.published_at || item.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: item.scheduled_for || undefined,
        status: item.status || 'public',
        isHD: item.is_hd || false,
        isPremium: item.is_premium || false,
        tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
        category: item.category || 'uncategorized',
        creator: {
          id: item.profiles?.id || 'unknown',
          email: '',
          avatar: (item.profiles?.avatar_url) || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      set({ favorites, isLoading: false });
    } catch (error) {
      console.error('Error fetching favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  addToFavorites: async (videoId: string) => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to add favorites' });
      return false;
    }

    try {
      // Check if already in favorites
      const { data: existingFavorite, error: checkError } = await supabase
        .from('favorites')
        .select('*')
        .eq('user_id', user.id)
        .eq('video_id', videoId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is expected
        throw new Error(checkError.message);
      }

      // If already a favorite, return true
      if (existingFavorite) {
        return true;
      }

      // Add to favorites
      const { error } = await supabase
        .from('favorites')
        .insert({
          user_id: user.id,
          video_id: videoId,
          created_at: new Date().toISOString()
        });

      if (error) {
        throw new Error(error.message);
      }

      // Refresh favorites
      await get().fetchFavorites();

      return true;
    } catch (error) {
      console.error('Error adding to favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  removeFromFavorites: async (videoId: string) => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to remove favorites' });
      return false;
    }

    try {
      // Remove from favorites
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id)
        .eq('video_id', videoId);

      if (error) {
        throw new Error(error.message);
      }

      // Update local state
      set(state => ({
        favorites: state.favorites.filter(video => video.id !== videoId)
      }));

      return true;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  isFavorite: (videoId: string) => {
    return get().favorites.some(video => video.id === videoId);
  }
}));
