import { create } from 'zustand';
import { supabase, getVideoQuery } from '../lib/supabase';
import { Video, VideoStatus, VideoAnalytics } from '../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../utils/mediaUtils';
import { useUserPreferencesStore } from './userPreferencesStore';
import { groupVideosBySeries, VideoSeries } from '../utils/videoGrouping';

interface SortOptions {
  field: 'title' | 'views' | 'likes' | 'createdAt' | 'status';
  direction: 'asc' | 'desc';
}

interface FilterOptions {
  category?: string;
  status?: VideoStatus;
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

interface VideoState {
  videos: Video[];
  userVideos: Video[];
  selectedVideoIds: string[];
  featuredVideo: Video | null;
  trendingVideos: Video[];
  recommendedVideos: Video[];
  continueWatchingVideos: Video[];
  sortOptions: SortOptions;
  filterOptions: FilterOptions;
  pagination: PaginationState;
  isLoading: boolean;
  error: string | null;

  // Fetch operations
  fetchVideos: (category?: string, page?: number, pageSize?: number) => Promise<void>;
  fetchAllVideos: (page?: number, pageSize?: number) => Promise<void>;
  fetchVideoById: (id: string) => Promise<Video | null>;
  fetchFeaturedVideo: () => Promise<void>;
  fetchTrendingVideos: () => Promise<void>;
  fetchRecommendedVideos: () => Promise<void>;
  fetchUserVideos: (sort?: SortOptions, filter?: FilterOptions, page?: number, pageSize?: number) => Promise<void>;
  fetchVideoAnalytics: (id: string) => Promise<VideoAnalytics | null>;
  fetchContinueWatchingVideos: () => Promise<void>;
  fetchPersonalizedContent: () => Promise<void>;

  // Pagination actions
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;

  // Update operations
  updateVideo: (id: string, updates: Partial<Video>) => Promise<boolean>;
  updateVideoStatus: (id: string, status: VideoStatus, scheduledFor?: string) => Promise<boolean>;
  deleteVideo: (id: string) => Promise<boolean>;
  incrementVideoViews: (id: string) => Promise<boolean>;

  // Batch operations
  selectVideo: (id: string, selected: boolean) => void;
  selectAllVideos: (selected: boolean) => void;
  batchDeleteVideos: () => Promise<boolean>;
  batchUpdateVideosStatus: (status: VideoStatus) => Promise<boolean>;
  batchUpdateVideosCategory: (category: string) => Promise<boolean>;

  // Sort and filter
  setSortOptions: (options: SortOptions) => void;
  setFilterOptions: (options: FilterOptions) => void;
}

export const useVideoStore = create<VideoState>((set, get) => ({
  videos: [],
  userVideos: [],
  selectedVideoIds: [],
  featuredVideo: null,
  trendingVideos: [],
  recommendedVideos: [],
  continueWatchingVideos: [],
  sortOptions: {
    field: 'createdAt',
    direction: 'desc'
  },
  filterOptions: {},
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 12
  },
  isLoading: false,
  error: null,

  fetchVideos: async (category?: string, page?: number, pageSize?: number) => {
    set({ isLoading: true, error: null });
    try {
      // Get pagination values from state or use provided values
      const currentPage = page || get().pagination.currentPage;
      const itemsPerPage = pageSize || get().pagination.pageSize;

      // Calculate range for pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // Handle special categories with custom logic
      if (category === 'hot') {
        // For "hot" category, get videos with high views and recent activity
        const { data, error } = await getVideoQuery(
          supabase.from('videos')
        )
          .order('views', { ascending: false })
          .range(from, to);

        if (error) {
          throw new Error(error.message);
        }

        // Get total count for hot videos (all videos)
        const { count } = await supabase
          .from('videos')
          .select('id', { count: 'exact' });

        const totalCount = count ?? 0;
        const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

        const videos: Video[] = data.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
          videoUrl: getWorkingVideoUrl(item.video_url || ''),
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at || item.created_at,
          publishedAt: item.published_at || item.created_at,
          scheduledFor: item.scheduled_for || undefined,
          status: item.status || 'public',
          isHD: item.is_hd || false,
          isPremium: item.is_premium || false,
          tags: Array.isArray(item.tags) ? item.tags : [],
          category: 'hot',
          originalCategory: item.category || 'uncategorized',
          creator: {
            id: item.creator?.id || '',
            email: '',
            avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          }
        }));

        set({
          videos,
          pagination: {
            currentPage,
            totalPages,
            totalCount,
            pageSize: itemsPerPage
          },
          isLoading: false
        });
        return;
      }

      if (category === 'new') {
        // For "new" category, get recently uploaded videos
        const { data, error } = await getVideoQuery(
          supabase.from('videos')
        )
          .order('created_at', { ascending: false })
          .range(from, to);

        if (error) {
          throw new Error(error.message);
        }

        // Get total count for new videos (all videos)
        const { count } = await supabase
          .from('videos')
          .select('id', { count: 'exact' });

        const totalCount = count ?? 0;
        const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

        const videos: Video[] = data.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
          videoUrl: getWorkingVideoUrl(item.video_url || ''),
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at || item.created_at,
          publishedAt: item.published_at || item.created_at,
          scheduledFor: item.scheduled_for || undefined,
          status: item.status || 'public',
          isHD: item.is_hd || false,
          isPremium: item.is_premium || false,
          tags: Array.isArray(item.tags) ? item.tags : [],
          category: 'new',
          originalCategory: item.category || 'uncategorized',
          creator: {
            id: item.creator?.id || '',
            email: '',
            avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          }
        }));

        set({
          videos,
          pagination: {
            currentPage,
            totalPages,
            totalCount,
            pageSize: itemsPerPage
          },
          isLoading: false
        });
        return;
      }

      if (category === 'trending') {
        // For "trending" category, get videos with high views in recent time
        const { data, error } = await getVideoQuery(
          supabase.from('videos')
        )
          .order('views', { ascending: false })
          .range(from, to);

        if (error) {
          throw new Error(error.message);
        }

        // Get total count for trending videos (all videos)
        const { count } = await supabase
          .from('videos')
          .select('id', { count: 'exact' });

        const totalCount = count ?? 0;
        const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

        const videos: Video[] = data.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
          videoUrl: getWorkingVideoUrl(item.video_url || ''),
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at || item.created_at,
          publishedAt: item.published_at || item.created_at,
          scheduledFor: item.scheduled_for || undefined,
          status: item.status || 'public',
          isHD: item.is_hd || false,
          isPremium: item.is_premium || false,
          tags: Array.isArray(item.tags) ? item.tags : [],
          category: 'trending',
          originalCategory: item.category || 'uncategorized',
          creator: {
            id: item.creator?.id || '',
            email: '',
            avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          }
        }));

        set({
          videos,
          pagination: {
            currentPage,
            totalPages,
            totalCount,
            pageSize: itemsPerPage
          },
          isLoading: false
        });
        return;
      }

      // Regular category filtering
      // First, get the total count for pagination
      let countQuery = supabase
        .from('videos')
        .select('id', { count: 'exact' });

      if (category && category !== 'all') {
        countQuery = countQuery.eq('category', category);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw new Error(countError.message);
      }

      // Now get the actual data with pagination
      let query = supabase.from('videos');

      // Use the helper function to standardize the query
      query = getVideoQuery(query)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (category && category !== 'all') {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Calculate total pages - ensure totalCount is properly initialized
      const totalCount = count ?? 0;
      const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

      // Transform the data to match our Video type
      const videos: Video[] = data.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(item.video_url || ''),
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: item.published_at || item.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: item.scheduled_for || undefined,
        status: item.status || 'public',
        isHD: item.is_hd || false,
        isPremium: item.is_premium || false,
        tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
        category: item.category || 'uncategorized',
        creator: {
          id: item.creator?.id || '',
          email: '',
          avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      // Update state with videos and pagination info
      set({
        videos,
        pagination: {
          currentPage,
          totalPages,
          totalCount,
          pageSize: itemsPerPage
        },
        isLoading: false
      });
    } catch (error) {
      // console.error('Error fetching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchAllVideos: async (page?: number, pageSize?: number) => {
    set({ isLoading: true, error: null });
    try {
      // Get pagination values from state or use provided values
      const currentPage = page || get().pagination.currentPage;
      const itemsPerPage = pageSize || get().pagination.pageSize;

      // Calculate range for pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // First, get the total count for pagination
      const { count, error: countError } = await supabase
        .from('videos')
        .select('id', { count: 'exact' });

      if (countError) {
        throw new Error(countError.message);
      }

      // Now get the actual data with pagination
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            id,
            username,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        throw new Error(error.message);
      }

      // Calculate total pages - ensure totalCount is properly initialized
      const totalCount = count ?? 0;
      const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

      // Transform the data to match our Video type
      const videos: Video[] = data.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(item.video_url || ''),
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: item.published_at || item.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: item.scheduled_for || undefined,
        status: item.status || 'public',
        isHD: item.is_hd || false,
        isPremium: item.is_premium || false,
        tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
        category: item.category || 'uncategorized',
        creator: {
          id: item.profiles?.id || '',
          email: '',
          avatar: item.profiles?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      // Update state with videos and pagination info
      set({
        videos,
        pagination: {
          currentPage,
          totalPages,
          totalCount,
          pageSize: itemsPerPage
        },
        isLoading: false
      });
    } catch (error) {
      // console.error('Error fetching all videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchVideoById: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const { data, error } = await getVideoQuery(
        supabase.from('videos')
      )
        .eq('id', id)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (!data) {
        return null;
      }

      // Transform the data to match our Video type
      const video: Video = {
        id: data.id,
        title: data.title,
        description: data.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(data.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(data.video_url || ''),
        duration: data.duration || 0,
        views: data.views || 0,
        likes: data.likes || 0,
        createdAt: data.created_at,
        updatedAt: data.updated_at || data.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: data.published_at || data.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: data.scheduled_for || undefined,
        status: data.status || 'public',
        isHD: data.is_hd || false,
        isPremium: data.is_premium || false,
        tags: Array.isArray(data.tags) ? data.tags : [], // Ensure tags is an array
        category: data.category || 'uncategorized',
        creator: {
          id: data.creator?.id || '',
          email: '',
          avatar: data.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      };

      set({ isLoading: false });
      return video;
    } catch (error) {
      console.error('Error fetching video by ID:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return null;
    }
  },

  fetchFeaturedVideo: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data, error } = await getVideoQuery(
        supabase.from('videos')
      )
        .order('views', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Video type
      const featuredVideo: Video = {
        id: data.id,
        title: data.title,
        description: data.description || '',
        thumbnailUrl: data.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
        videoUrl: data.video_url,
        duration: data.duration || 0,
        views: data.views || 0,
        likes: data.likes || 0,
        createdAt: data.created_at,
        updatedAt: data.updated_at || data.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: data.published_at || data.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: data.scheduled_for || undefined,
        status: data.status || 'public',
        isHD: data.is_hd || false,
        isPremium: data.is_premium || false,
        tags: Array.isArray(data.tags) ? data.tags : [], // Ensure tags is an array
        category: data.category || 'uncategorized',
        creator: {
          id: data.creator?.id || '',
          email: '',
          avatar: data.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      };

      set({ featuredVideo, isLoading: false });
    } catch (error) {
      console.error('Error fetching featured video:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchTrendingVideos: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data, error } = await getVideoQuery(
        supabase.from('videos')
      )
        .order('views', { ascending: false })
        .limit(8);

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Video type and set category to 'trending'
      const trendingVideos: Video[] = data.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: item.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
        videoUrl: item.video_url,
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
        publishedAt: item.published_at || item.created_at, // Fallback to created_at if published_at is missing
        scheduledFor: item.scheduled_for || undefined,
        status: item.status || 'public',
        isHD: item.is_hd || false,
        isPremium: item.is_premium || false,
        tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
        category: 'trending', // Set category to 'trending' for all trending videos
        originalCategory: item.category || 'uncategorized', // Store original category
        creator: {
          id: item.creator?.id || '',
          email: '',
          avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      // Also update the videos in the database to have 'trending' category
      // This is done in batches to avoid too many requests
      const updatePromises = data.map(item =>
        supabase
          .from('videos')
          .update({ category: 'trending' })
          .eq('id', item.id)
      );

      // Execute all updates in parallel
      await Promise.all(updatePromises);

      set({ trendingVideos, isLoading: false });
    } catch (error) {
      console.error('Error fetching trending videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchRecommendedVideos: async () => {
    set({ isLoading: true, error: null });
    try {
      // Get user preferences for recommendation algorithm
      const userPreferences = useUserPreferencesStore.getState();

      // Get most viewed categories (not used since category column doesn't exist)
      // const preferredCategories = userPreferences.getMostViewedCategories(3);

      // Get watch history
      const watchHistory = userPreferences.watchHistory;
      const watchedVideoIds = Object.keys(watchHistory);

      // Get liked videos
      const likedVideoIds = userPreferences.likedVideos;

      // First, get a set of the most watched videos (top 10)
      // These will be prioritized in recommendations
      const topViewedQuery = getVideoQuery(
        supabase.from('videos')
      )
        .order('views', { ascending: false })
        .limit(10);

      // If user has watched videos, exclude them from recommendations
      if (watchedVideoIds.length > 0) {
        topViewedQuery.not('id', 'in', `(${watchedVideoIds.join(',')})`);
      }

      const { data: topViewedData, error: topViewedError } = await topViewedQuery;

      if (topViewedError) {
        throw new Error(topViewedError.message);
      }

      // Then, get a mix of personalized recommendations
      let personalizedQuery = getVideoQuery(
        supabase.from('videos')
      );

      // If user has watched videos, exclude them from recommendations
      if (watchedVideoIds.length > 0) {
        personalizedQuery = personalizedQuery.not('id', 'in', `(${watchedVideoIds.join(',')})`);
      }

      // Exclude the top viewed videos we already fetched
      if (topViewedData && topViewedData.length > 0) {
        const topViewedIds = topViewedData.map(video => video.id);
        personalizedQuery = personalizedQuery.not('id', 'in', `(${topViewedIds.join(',')})`);
      }

      // Order by a combination of views and recency
      personalizedQuery = personalizedQuery.order('views', { ascending: false }).limit(15);

      const { data: personalizedData, error: personalizedError } = await personalizedQuery;

      if (personalizedError) {
        throw new Error(personalizedError.message);
      }

      // Combine the datasets
      const combinedData = [...(topViewedData || []), ...(personalizedData || [])];

      // If we don't have enough recommendations, fetch more without filters
      if (combinedData.length < 8) {
        const additionalQuery = getVideoQuery(
          supabase.from('videos')
        )
          .order('views', { ascending: false })
          .limit(12);

        if (watchedVideoIds.length > 0) {
          additionalQuery.not('id', 'in', `(${watchedVideoIds.join(',')})`);
        }

        // Exclude videos we already have
        if (combinedData.length > 0) {
          const existingIds = combinedData.map(video => video.id);
          additionalQuery.not('id', 'in', `(${existingIds.join(',')})`);
        }

        const { data: additionalData, error: additionalError } = await additionalQuery;

        if (!additionalError && additionalData) {
          combinedData.push(...additionalData);
        }
      }

      // Transform the data to match our Video type
      let recommendedVideos: Video[] = combinedData.map(item => {
        // Calculate similarity score based on user preferences
        let similarityScore = 0;

        // Skip category boost since the column doesn't exist
        // if (preferredCategories.includes(item.category)) {
        //   similarityScore += 0.5;
        // }

        // Boost score for videos from creators of liked videos
        if (likedVideoIds.length > 0) {
          const creatorHasLikedVideos = combinedData.some(video =>
            likedVideoIds.includes(video.id) && video.user_id === item.user_id
          );

          if (creatorHasLikedVideos) {
            similarityScore += 0.3;
          }
        }

        // Significantly boost score for popular videos (increased weight)
        similarityScore += Math.min(item.views / 5000, 0.5);

        // Add recency boost (newer videos get a small boost)
        const createdDate = new Date(item.created_at);
        const now = new Date();
        const ageInDays = Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
        // Videos less than 30 days old get a boost, with newer videos getting more
        if (ageInDays < 30) {
          similarityScore += 0.2 * (1 - ageInDays / 30);
        }

        // Get the creator data safely
        const creator = item.creator;
        const profileId = creator && typeof creator === 'object' && 'id' in creator ? creator.id : '';
        const avatarUrl = creator && typeof creator === 'object' && 'avatar_url' in creator ? creator.avatar_url : null;

        return {
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: item.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
          videoUrl: item.video_url,
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          // Add default values for missing fields
          publishedAt: new Date().toISOString(),
          scheduledFor: undefined,
          status: 'public' as VideoStatus,
          isHD: item.is_hd || false,
          isPremium: false,
          tags: [],
          category: 'uncategorized',
          creator: {
            id: profileId,
            email: '',
            avatar: avatarUrl || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          },
          similarity: similarityScore
        };
      });

      // First, ensure all videos have a similarity score
      recommendedVideos = recommendedVideos.map(video => ({
        ...video,
        similarity: video.similarity || 0
      }));

      // Create a special sorting function that prioritizes views but also considers similarity
      recommendedVideos.sort((a, b) => {
        // First, prioritize videos from the top viewed query (they should already be at the start of the array)
        if (a.views > 1000 && b.views <= 1000) return -1;
        if (b.views > 1000 && a.views <= 1000) return 1;

        // For videos with very high view counts, sort directly by views
        if (a.views > 5000 && b.views > 5000) {
          return b.views - a.views;
        }

        // For other videos, use the similarity score which includes view count weight
        return (b.similarity || 0) - (a.similarity || 0);
      });

      // Import the grouping function at the top of the file
      // This is just a comment - the actual import should be at the top of the file

      // Limit to 15 recommendations before grouping
      recommendedVideos = recommendedVideos.slice(0, 15);

      // Group videos with the same captions based on their numbering
      // Note: We don't actually modify the recommendedVideos array here
      // The grouping will be done in the VideoGrid component

      set({ recommendedVideos, isLoading: false });
    } catch (error) {
      console.error('Error fetching recommended videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchUserVideos: async (sort?: SortOptions, filter?: FilterOptions, page?: number, pageSize?: number) => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to view your videos');
      }

      // Use provided sort options or get from state
      const sortOptions = sort || get().sortOptions;

      // Use provided filter options or get from state
      const filterOptions = filter || get().filterOptions;

      // Get pagination values from state or use provided values
      const currentPage = page || get().pagination.currentPage;
      const itemsPerPage = pageSize || get().pagination.pageSize;

      // Calculate range for pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // First, get the total count for pagination
      let countQuery = supabase
        .from('videos')
        .select('id', { count: 'exact' })
        .eq('user_id', user.id);

      // Apply filters to count query if provided
      // Skip category filter since the column doesn't exist
      // if (filterOptions.category) {
      //   countQuery = countQuery.eq('category', filterOptions.category);
      // }

      // Skip status filter since the column doesn't exist
      // if (filterOptions.status) {
      //   countQuery = countQuery.eq('status', filterOptions.status);
      // }

      if (filterOptions.search) {
        countQuery = countQuery.ilike('title', `%${filterOptions.search}%`);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw new Error(countError.message);
      }

      // Start building the query for actual data
      let query = getVideoQuery(
        supabase.from('videos')
      )
        .eq('user_id', user.id)
        .range(from, to);

      // Apply filters if provided
      // Skip category filter since the column doesn't exist
      // if (filterOptions.category) {
      //   query = query.eq('category', filterOptions.category);
      // }

      // Skip status filter since the column doesn't exist
      // if (filterOptions.status) {
      //   query = query.eq('status', filterOptions.status);
      // }

      if (filterOptions.search) {
        query = query.or(`title.ilike.%${filterOptions.search}%,description.ilike.%${filterOptions.search}%`);
      }

      if (filterOptions.dateRange) {
        query = query
          .gte('created_at', filterOptions.dateRange.start)
          .lte('created_at', filterOptions.dateRange.end);
      }

      // Apply sorting
      const sortField = sortOptions.field === 'createdAt' ? 'created_at' : sortOptions.field;
      query = query.order(sortField, { ascending: sortOptions.direction === 'asc' });

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Calculate total pages - ensure totalCount is properly initialized
      const totalCount = count ?? 0;
      const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

      // Transform the data to match our Video type
      const userVideos: Video[] = data.map(item => {
        // Get the creator data safely
        const creator = item.creator;
        const profileId = creator && typeof creator === 'object' && 'id' in creator ? creator.id : '';
        const avatarUrl = creator && typeof creator === 'object' && 'avatar_url' in creator ? creator.avatar_url : null;

        return {
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: item.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
          videoUrl: item.video_url,
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          // Add default values for missing fields
          publishedAt: new Date().toISOString(),
          scheduledFor: undefined,
          status: 'public' as VideoStatus,
          isHD: item.is_hd || false,
          isPremium: false,
          tags: [],
          category: 'uncategorized',
          creator: {
            id: profileId,
            email: '',
            avatar: avatarUrl || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          }
        };
      });

      set({
        userVideos,
        pagination: {
          currentPage,
          totalPages,
          totalCount,
          pageSize: itemsPerPage
        },
        isLoading: false
      });
    } catch (error) {
      // console.error('Error fetching user videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  updateVideo: async (id: string, updates: Partial<Video>) => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to update videos');
      }

      // Prepare the update data
      const updateData: any = {};

      // Map the Video type fields to database fields
      if (updates.title !== undefined) updateData.title = updates.title;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.category !== undefined) updateData.category = updates.category;
      if (updates.thumbnailUrl !== undefined) updateData.thumbnail_url = updates.thumbnailUrl;

      // Update the video in the database
      const { error } = await supabase
        .from('videos')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id); // Ensure the user can only update their own videos

      if (error) {
        throw new Error(error.message);
      }

      // Refresh the user's videos
      await get().fetchUserVideos();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error updating video:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  deleteVideo: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to delete videos');
      }

      // Get the video to find the storage paths
      const { data: video, error: fetchError } = await supabase
        .from('videos')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id) // Ensure the user can only delete their own videos
        .single();

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!video) {
        throw new Error('Video not found or you do not have permission to delete it');
      }

      // Extract the file paths from the URLs with proper validation
      let videoPath = null;
      let thumbnailPath = null;

      // Validate and extract video URL path
      if (video.video_url && typeof video.video_url === 'string' && video.video_url.trim()) {
        try {
          const videoUrl = new URL(video.video_url);
          videoPath = videoUrl.pathname.split('/').slice(2).join('/');
        } catch (error) {
          console.error('Invalid video URL:', video.video_url, error);
          // Continue without deleting the file if URL is invalid
        }
      }

      // Validate and extract thumbnail URL path
      if (video.thumbnail_url && typeof video.thumbnail_url === 'string' && video.thumbnail_url.trim()) {
        try {
          const thumbnailUrl = new URL(video.thumbnail_url);
          thumbnailPath = thumbnailUrl.pathname.split('/').slice(2).join('/');
        } catch (error) {
          console.error('Invalid thumbnail URL:', video.thumbnail_url, error);
          // Continue without deleting the file if URL is invalid
        }
      }

      // Delete the video file from storage if path is valid
      if (videoPath) {
        const { error: videoDeleteError } = await supabase.storage
          .from('videos')
          .remove([videoPath]);

        if (videoDeleteError) {
          console.error('Error deleting video file:', videoDeleteError);
          // Continue even if file deletion fails
        }
      } else {
        console.warn('Skipping video file deletion - invalid or missing video URL');
      }

      // Delete the thumbnail file from storage if it exists and path is valid
      if (thumbnailPath) {
        const { error: thumbnailDeleteError } = await supabase.storage
          .from('thumbnails')
          .remove([thumbnailPath]);

        if (thumbnailDeleteError) {
          console.error('Error deleting thumbnail file:', thumbnailDeleteError);
          // Continue even if file deletion fails
        }
      }

      // Delete the video record from the database
      const { error: deleteError } = await supabase
        .from('videos')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Refresh the user's videos
      await get().fetchUserVideos();

      // Remove from selected videos if it was selected
      if (get().selectedVideoIds.includes(id)) {
        set(state => ({
          selectedVideoIds: state.selectedVideoIds.filter(videoId => videoId !== id)
        }));
      }

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error deleting video:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  updateVideoStatus: async (id: string, status: VideoStatus, scheduledFor?: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to update video status');
      }

      // Prepare the update data
      const updateData: any = { status };

      // If status is scheduled, we need a scheduled_for date
      if (status === 'scheduled' && scheduledFor) {
        updateData.scheduled_for = scheduledFor;
      } else if (status === 'public') {
        // If status is public, set published_at to now if not already set
        updateData.published_at = new Date().toISOString();
      }

      // Update the video in the database
      const { error } = await supabase
        .from('videos')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id); // Ensure the user can only update their own videos

      if (error) {
        throw new Error(error.message);
      }

      // Refresh the user's videos
      await get().fetchUserVideos();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error updating video status:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  fetchVideoAnalytics: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to view video analytics');
      }

      // In a real app, we would fetch analytics from a dedicated analytics service
      // For this demo, we'll generate mock analytics data

      // First, check if the video exists and belongs to the user
      const { data: video, error: fetchError } = await supabase
        .from('videos')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!video) {
        throw new Error('Video not found or you do not have permission to view its analytics');
      }

      // Generate mock analytics data
      const viewsLast7Days = Math.floor(Math.random() * 1000);
      const viewsLast30Days = viewsLast7Days + Math.floor(Math.random() * 2000);
      const likesLast7Days = Math.floor(viewsLast7Days * 0.1);
      const likesLast30Days = Math.floor(viewsLast30Days * 0.1);

      // Generate daily view data for the last 30 days
      const viewsByDay = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 100)
        };
      });

      // Generate country data
      const viewsByCountry = [
        { country: 'United States', count: Math.floor(Math.random() * 500) },
        { country: 'United Kingdom', count: Math.floor(Math.random() * 300) },
        { country: 'Canada', count: Math.floor(Math.random() * 200) },
        { country: 'Australia', count: Math.floor(Math.random() * 150) },
        { country: 'Germany', count: Math.floor(Math.random() * 100) }
      ];

      // Generate device data
      const viewsByDevice = [
        { device: 'Mobile', count: Math.floor(Math.random() * 600) },
        { device: 'Desktop', count: Math.floor(Math.random() * 400) },
        { device: 'Tablet', count: Math.floor(Math.random() * 200) },
        { device: 'Smart TV', count: Math.floor(Math.random() * 100) }
      ];

      const analytics: VideoAnalytics = {
        viewsLast7Days,
        viewsLast30Days,
        likesLast7Days,
        likesLast30Days,
        viewsByDay,
        viewsByCountry,
        viewsByDevice
      };

      set({ isLoading: false });
      return analytics;
    } catch (error) {
      console.error('Error fetching video analytics:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return null;
    }
  },

  fetchContinueWatchingVideos: async () => {
    set({ isLoading: true, error: null });
    try {
      // Get video IDs from user preferences store
      const continueWatchingIds = await useUserPreferencesStore.getState().getContinueWatchingVideos();

      if (continueWatchingIds.length === 0) {
        set({ continueWatchingVideos: [], isLoading: false });
        return;
      }

      // Fetch the videos by IDs
      const { data, error } = await getVideoQuery(
        supabase.from('videos')
      )
        .in('id', continueWatchingIds)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Video type
      const videos: Video[] = data.map(item => {
        // Get watch progress for this video
        const progress = useUserPreferencesStore.getState().getWatchProgress(item.id);

        // Get the creator data safely
        const creator = item.creator;
        const profileId = creator && typeof creator === 'object' && 'id' in creator ? creator.id : '';
        const avatarUrl = creator && typeof creator === 'object' && 'avatar_url' in creator ? creator.avatar_url : null;

        return {
          id: item.id,
          title: item.title,
          description: item.description || '',
          thumbnailUrl: item.thumbnail_url || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail',
          videoUrl: item.video_url,
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          // Add default values for missing fields
          publishedAt: new Date().toISOString(),
          scheduledFor: undefined,
          status: 'public' as VideoStatus,
          isHD: item.is_hd || false,
          isPremium: false,
          tags: [],
          category: 'uncategorized',
          creator: {
            id: profileId,
            email: '',
            avatar: avatarUrl || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          },
          progress: progress || undefined
        };
      });

      // Sort by last watched (most recent first)
      videos.sort((a, b) => {
        if (!a.progress || !b.progress) return 0;
        return new Date(b.progress.lastWatched).getTime() - new Date(a.progress.lastWatched).getTime();
      });

      set({ continueWatchingVideos: videos, isLoading: false });
    } catch (error) {
      console.error('Error fetching continue watching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },



  fetchPersonalizedContent: async () => {
    set({ isLoading: true });

    try {
      // Fetch all personalized content in parallel
      await Promise.all([
        get().fetchContinueWatchingVideos(),
        get().fetchRecommendedVideos()
      ]);

      set({ isLoading: false });
    } catch (error) {
      console.error('Error fetching personalized content:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  // Batch operations
  selectVideo: (id: string, selected: boolean) => {
    set(state => {
      if (selected) {
        // Add to selected videos if not already selected
        if (!state.selectedVideoIds.includes(id)) {
          return { selectedVideoIds: [...state.selectedVideoIds, id] };
        }
      } else {
        // Remove from selected videos
        return { selectedVideoIds: state.selectedVideoIds.filter(videoId => videoId !== id) };
      }
      return state;
    });
  },

  selectAllVideos: (selected: boolean) => {
    if (selected) {
      // Select all videos
      const allVideoIds = get().userVideos.map(video => video.id);
      set({ selectedVideoIds: allVideoIds });
    } else {
      // Deselect all videos
      set({ selectedVideoIds: [] });
    }
  },

  batchDeleteVideos: async () => {
    const selectedIds = get().selectedVideoIds;

    if (selectedIds.length === 0) {
      return false;
    }

    set({ isLoading: true, error: null });

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to delete videos');
      }

      // Get the videos to find the storage paths
      const { data: videos, error: fetchError } = await supabase
        .from('videos')
        .select('*')
        .in('id', selectedIds)
        .eq('user_id', user.id); // Ensure the user can only delete their own videos

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!videos || videos.length === 0) {
        throw new Error('No videos found or you do not have permission to delete them');
      }

      // Delete each video's files from storage
      for (const video of videos) {
        let videoPath = null;
        let thumbnailPath = null;

        // Validate and extract video URL path
        if (video.video_url && typeof video.video_url === 'string' && video.video_url.trim()) {
          try {
            const videoUrl = new URL(video.video_url);
            videoPath = videoUrl.pathname.split('/').slice(2).join('/');
          } catch (error) {
            console.error(`Invalid video URL for ${video.id}:`, video.video_url, error);
            // Continue without deleting the file if URL is invalid
          }
        }

        // Validate and extract thumbnail URL path
        if (video.thumbnail_url && typeof video.thumbnail_url === 'string' && video.thumbnail_url.trim()) {
          try {
            const thumbnailUrl = new URL(video.thumbnail_url);
            thumbnailPath = thumbnailUrl.pathname.split('/').slice(2).join('/');
          } catch (error) {
            console.error(`Invalid thumbnail URL for ${video.id}:`, video.thumbnail_url, error);
            // Continue without deleting the file if URL is invalid
          }
        }

        // Delete the video file from storage if path is valid
        if (videoPath) {
          const { error: videoDeleteError } = await supabase.storage
            .from('videos')
            .remove([videoPath]);

          if (videoDeleteError) {
            console.error(`Error deleting video file for ${video.id}:`, videoDeleteError);
            // Continue even if file deletion fails
          }
        } else {
          console.warn(`Skipping video file deletion for ${video.id} - invalid or missing video URL`);
        }

        // Delete the thumbnail file from storage if it exists and path is valid
        if (thumbnailPath) {
          const { error: thumbnailDeleteError } = await supabase.storage
            .from('thumbnails')
            .remove([thumbnailPath]);

          if (thumbnailDeleteError) {
            console.error(`Error deleting thumbnail file for ${video.id}:`, thumbnailDeleteError);
            // Continue even if file deletion fails
          }
        }
      }

      // Delete the video records from the database
      const { error: deleteError } = await supabase
        .from('videos')
        .delete()
        .in('id', selectedIds)
        .eq('user_id', user.id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Refresh the user's videos
      await get().fetchUserVideos();

      // Clear selected videos
      set({ selectedVideoIds: [] });

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error batch deleting videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  batchUpdateVideosStatus: async (status: VideoStatus) => {
    const selectedIds = get().selectedVideoIds;

    if (selectedIds.length === 0) {
      return false;
    }

    set({ isLoading: true, error: null });

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to update videos');
      }

      // Prepare the update data
      const updateData: any = { status };

      // If status is public, set published_at to now
      if (status === 'public') {
        updateData.published_at = new Date().toISOString();
      }

      // Update the videos in the database
      const { error } = await supabase
        .from('videos')
        .update(updateData)
        .in('id', selectedIds)
        .eq('user_id', user.id); // Ensure the user can only update their own videos

      if (error) {
        throw new Error(error.message);
      }

      // Refresh the user's videos
      await get().fetchUserVideos();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error batch updating videos:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  batchUpdateVideosCategory: async (_category: string) => {
    const selectedIds = get().selectedVideoIds;

    if (selectedIds.length === 0) {
      return false;
    }

    set({ isLoading: true, error: null });

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('You must be logged in to update videos');
      }

      // Skip database update since the category column doesn't exist
      // Simulate a successful update
      // No database operation needed

      // Refresh the user's videos
      await get().fetchUserVideos();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error batch updating videos category:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
      return false;
    }
  },

  incrementVideoViews: async (id: string) => {
    try {
      // First, get the current view count
      const { data, error: fetchError } = await supabase
        .from('videos')
        .select('views')
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching video views:', fetchError);
        return false;
      }

      // Calculate the new view count
      const currentViews = data?.views || 0;
      const newViews = currentViews + 1;

      // Update the view count in the database
      const { error: updateError } = await supabase
        .from('videos')
        .update({ views: newViews })
        .eq('id', id);

      if (updateError) {
        console.error('Error updating video views:', updateError);
        return false;
      }

      // Update the video in the local state if it exists
      set(state => {
        // Update in videos array
        const updatedVideos = state.videos.map(video =>
          video.id === id ? { ...video, views: newViews } : video
        );

        // Update in trending videos array
        const updatedTrendingVideos = state.trendingVideos.map(video =>
          video.id === id ? { ...video, views: newViews } : video
        );

        // Update in recommended videos array
        const updatedRecommendedVideos = state.recommendedVideos.map(video =>
          video.id === id ? { ...video, views: newViews } : video
        );

        // Update in continue watching videos array
        const updatedContinueWatchingVideos = state.continueWatchingVideos.map(video =>
          video.id === id ? { ...video, views: newViews } : video
        );

        // Update in featured video if it matches
        let updatedFeaturedVideo = state.featuredVideo;
        if (state.featuredVideo && state.featuredVideo.id === id) {
          updatedFeaturedVideo = { ...state.featuredVideo, views: newViews };
        }

        return {
          videos: updatedVideos,
          trendingVideos: updatedTrendingVideos,
          recommendedVideos: updatedRecommendedVideos,
          continueWatchingVideos: updatedContinueWatchingVideos,
          featuredVideo: updatedFeaturedVideo
        };
      });

      return true;
    } catch (error) {
      console.error('Error incrementing video views:', error);
      return false;
    }
  },

  // Sort and filter
  setSortOptions: (options: SortOptions) => {
    set({ sortOptions: options });
    // Refetch videos with new sort options
    get().fetchUserVideos(options, get().filterOptions);
  },

  setFilterOptions: (options: FilterOptions) => {
    set({ filterOptions: options });
    // Refetch videos with new filter options
    get().fetchUserVideos(get().sortOptions, options);
  },

  // Pagination
  setPage: (page: number) => {
    set(state => ({
      pagination: {
        ...state.pagination,
        currentPage: page
      }
    }));
    // Refetch videos with new page
    get().fetchVideos(undefined, page, get().pagination.pageSize);
  },

  setPageSize: (pageSize: number) => {
    set(state => ({
      pagination: {
        ...state.pagination,
        pageSize: pageSize,
        currentPage: 1 // Reset to first page when changing page size
      }
    }));
    // Refetch videos with new page size
    get().fetchVideos(undefined, 1, pageSize);
  },
}));
