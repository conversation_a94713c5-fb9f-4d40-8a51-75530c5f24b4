import React from 'react';
import { PlayCircle } from 'lucide-react';
import { Video } from '../../types';
import { formatCount, formatDuration } from '../../utils/formatters';
import OptimizedImage from './OptimizedImage';

interface VideoCardWithProgressProps {
  video: Video;
  onClick?: (video: Video) => void;
}

const VideoCardWithProgress: React.FC<VideoCardWithProgressProps> = ({ video, onClick }) => {
  const handleClick = () => {
    if (onClick) {
      onClick(video);
    }
  };

  return (
    <div
      className="relative overflow-hidden cursor-pointer mb-4"
      onClick={handleClick}
    >
      <div className="relative aspect-video overflow-hidden bg-gray-900">
        <OptimizedImage
          src={video.thumbnailUrl || 'https://via.placeholder.com/640x360?text=No+Thumbnail'}
          alt={video.title}
          fallbackSrc="https://via.placeholder.com/640x360?text=No+Thumbnail"
          className="w-full h-full object-cover"
          width={640}
          height={360}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
        />

        {/* Duration badge */}
        {video.duration > 0 && (
          <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
            {formatDuration(video.duration)}
          </div>
        )}

        {/* HD badge */}
        {video.isHD && (
          <div className="absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium">
            HD
          </div>
        )}

        {/* Progress bar */}
        {video.progress && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-700">
            <div
              className="h-full bg-blue-700"
              style={{ width: `${video.progress.percent}%` }}
            ></div>
          </div>
        )}
      </div>

      <div className="py-2">
        <h3 className="text-white font-medium line-clamp-2 text-sm">
          {video.title}
        </h3>
        <div className="flex items-center text-xs text-gray-400 mt-1">
          <span>{formatCount(video.views || 0)} Views</span>
        </div>

        {/* Continue watching info */}
        {video.progress && !video.progress.completed && (
          <div className="mt-1 text-xs text-blue-700">
            Continue watching ({Math.floor(video.progress.percent)}% complete)
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoCardWithProgress;
