/**
 * Utility functions for handling media (videos and images) in production
 */

/**
 * Check if a URL is accessible
 */
export const checkUrlAccessibility = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('URL accessibility check failed:', url, error);
    return false;
  }
};

/**
 * Validate and fix Supabase storage URLs
 */
export const validateSupabaseUrl = (url: string): string => {
  if (!url) return '';

  // Clean up any whitespace and newline characters that might be in the URL
  const cleanedUrl = url.trim().replace(/[\r\n\t]/g, '').replace(/%0A/g, '');

  // Check if it's already a valid Supabase URL
  if (cleanedUrl.includes('supabase.co/storage/v1/object/public/')) {
    return cleanedUrl;
  }

  // If it's a relative path, construct the full URL
  if (cleanedUrl.startsWith('/storage/') || cleanedUrl.startsWith('storage/')) {
    const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl.slice(1) : cleanedUrl;
    return `https://vsnsglgyapexhwyfylic.supabase.co/${cleanPath}`;
  }

  return cleanedUrl;
};

/**
 * Get a working thumbnail URL with fallbacks
 */
export const getWorkingThumbnailUrl = (thumbnailUrl: string): string => {
  // Validate and fix the URL first
  const validatedUrl = validateSupabaseUrl(thumbnailUrl);

  if (!validatedUrl) {
    return getPlaceholderThumbnail();
  }

  return validatedUrl;
};

/**
 * Get a working video URL with fallbacks
 */
export const getWorkingVideoUrl = (videoUrl: string): string => {
  // Validate and fix the URL first
  const validatedUrl = validateSupabaseUrl(videoUrl);

  if (!validatedUrl) {
    console.error('Invalid video URL:', videoUrl);
    return '';
  }

  return validatedUrl;
};

/**
 * Generate a placeholder thumbnail
 */
export const getPlaceholderThumbnail = (): string => {
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'%3E%3Crect width='640' height='360' fill='%23374151'/%3E%3Cg transform='translate(320,180)'%3E%3Ccircle cx='0' cy='0' r='30' fill='%236B7280'/%3E%3Cpolygon points='-10,-15 -10,15 20,0' fill='%23F3F4F6'/%3E%3C/g%3E%3Ctext x='320' y='320' font-family='Arial, sans-serif' font-size='18' fill='%239CA3AF' text-anchor='middle'%3ENo Thumbnail%3C/text%3E%3C/svg%3E`;
};

/**
 * Test if an image URL loads successfully
 */
export const testImageLoad = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;

    // Timeout after 10 seconds
    setTimeout(() => resolve(false), 10000);
  });
};

/**
 * Test if a video URL loads successfully
 */
export const testVideoLoad = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.onloadedmetadata = () => resolve(true);
    video.onerror = () => resolve(false);
    video.src = url;

    // Timeout after 15 seconds
    setTimeout(() => resolve(false), 15000);
  });
};

/**
 * Preload critical media assets
 */
export const preloadMedia = async (videos: Array<{thumbnailUrl: string, videoUrl: string}>) => {
  const preloadPromises = videos.slice(0, 3).map(async (video) => {
    // Preload thumbnails
    if (video.thumbnailUrl) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = getWorkingThumbnailUrl(video.thumbnailUrl);
      document.head.appendChild(link);
    }
  });

  await Promise.allSettled(preloadPromises);
};

/**
 * Fix common URL issues in production
 */
export const fixProductionUrls = (url: string): string => {
  if (!url) return '';

  // Remove any double slashes except after protocol
  const fixed = url.replace(/([^:]\/)\/+/g, '$1');

  // Ensure HTTPS in production
  if (fixed.startsWith('http://') && window.location.protocol === 'https:') {
    return fixed.replace('http://', 'https://');
  }

  return fixed;
};

/**
 * Get optimized image URL for different screen sizes
 */
export const getOptimizedImageUrl = (url: string, width?: number): string => {
  const validatedUrl = validateSupabaseUrl(url);

  if (!validatedUrl || !width) {
    return validatedUrl;
  }

  // For Supabase storage, we can add width parameters
  if (validatedUrl.includes('supabase.co/storage/v1/object/public/')) {
    try {
      const urlObj = new URL(validatedUrl);
      urlObj.searchParams.set('width', width.toString());
      urlObj.searchParams.set('quality', '80');
      return urlObj.toString();
    } catch (error) {
      console.error('Error optimizing image URL:', error);
      return validatedUrl;
    }
  }

  return validatedUrl;
};
