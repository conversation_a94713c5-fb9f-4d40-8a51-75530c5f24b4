import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { useAuthStore } from './authStore';

export interface Comment {
  id: string;
  videoId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  createdAt: string;
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
}

interface CommentState {
  comments: Comment[];
  isLoading: boolean;
  error: string | null;

  // Fetch operations
  fetchComments: (videoId: string) => Promise<void>;

  // Create/Update operations
  addComment: (videoId: string, content: string, anonymousName?: string) => Promise<Comment | null>;
  deleteComment: (commentId: string) => Promise<boolean>;
  likeComment: (commentId: string, isAnonymous?: boolean) => Promise<boolean>;
  unlikeComment: (commentId: string, isAnonymous?: boolean) => Promise<boolean>;

  // Reply operations
  addReply: (commentId: string, content: string, anonymousName?: string) => Promise<Comment | null>;
}

export const useCommentStore = create<CommentState>((set, get) => ({
  comments: [],
  isLoading: false,
  error: null,

  fetchComments: async (videoId: string) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would fetch from the database
      // For now, we'll simulate with mock data
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('video_id', videoId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      // If no comments in the database yet, return empty array
      if (!data || data.length === 0) {
        set({ comments: [], isLoading: false });
        return;
      }

      // Get liked comments from localStorage for anonymous users
      const likedComments = JSON.parse(localStorage.getItem('anonymousLikedComments') || '[]');
      const { user } = useAuthStore.getState();

      // Transform the data to match our Comment type
      const comments: Comment[] = data.map(item => ({
        id: item.id,
        videoId: item.video_id,
        userId: item.user_id,
        userName: item.user_name || 'Anonymous',
        userAvatar: item.user_avatar || 'https://placehold.co/150/gray/white?text=User',
        content: item.content,
        createdAt: item.created_at,
        likes: item.likes || 0,
        // Check if comment is liked by the current user (authenticated or anonymous)
        isLiked: user ? (item.is_liked || false) : likedComments.includes(item.id),
        replies: item.replies || []
      }));

      set({ comments, isLoading: false });
    } catch (error) {
      console.error('Error fetching comments:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  addComment: async (videoId: string, content: string, anonymousName?: string) => {
    const { user } = useAuthStore.getState();

    if (!content.trim()) {
      set({ error: 'Comment cannot be empty' });
      return null;
    }

    try {
      let data;
      let error;

      if (user) {
        // For authenticated users
        const result = await supabase
          .from('comments')
          .insert({
            video_id: videoId,
            user_id: user.id,
            user_name: user.email?.split('@')[0] || 'Anonymous',
            user_avatar: user.avatar || null,
            content: content.trim(),
            likes: 0,
            is_liked: false
          })
          .select()
          .single();

        data = result.data;
        error = result.error;
      } else {
        // For anonymous users
        if (!anonymousName || !anonymousName.trim()) {
          set({ error: 'Please provide a display name' });
          return null;
        }

        const result = await supabase
          .from('comments')
          .insert({
            video_id: videoId,
            user_id: 'anonymous',
            user_name: anonymousName.trim(),
            user_avatar: null,
            content: content.trim(),
            likes: 0,
            is_liked: false
          })
          .select()
          .single();

        data = result.data;
        error = result.error;
      }

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Comment type
      const newComment: Comment = {
        id: data.id,
        videoId: data.video_id,
        userId: data.user_id,
        userName: data.user_name || 'Anonymous',
        userAvatar: data.user_avatar || 'https://placehold.co/150/gray/white?text=User',
        content: data.content,
        createdAt: data.created_at,
        likes: data.likes || 0,
        isLiked: data.is_liked || false,
        replies: []
      };

      // Update the comments list
      set(state => ({
        comments: [newComment, ...state.comments],
        error: null
      }));

      return newComment;
    } catch (error) {
      console.error('Error adding comment:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return null;
    }
  },

  deleteComment: async (commentId: string) => {
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to delete a comment' });
      return false;
    }

    try {
      // First check if the comment belongs to the user
      const { data: commentData, error: fetchError } = await supabase
        .from('comments')
        .select('user_id')
        .eq('id', commentId)
        .single();

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (commentData.user_id !== user.id) {
        throw new Error('You can only delete your own comments');
      }

      // Delete the comment
      const { error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

      if (error) {
        throw new Error(error.message);
      }

      // Update the comments list
      set(state => ({
        comments: state.comments.filter(comment => comment.id !== commentId),
        error: null
      }));

      return true;
    } catch (error) {
      console.error('Error deleting comment:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  likeComment: async (commentId: string, isAnonymous = false) => {
    const { user } = useAuthStore.getState();

    if (!user && !isAnonymous) {
      set({ error: 'You must be logged in to like a comment' });
      return false;
    }

    try {
      // In a real app, we would use a junction table for likes
      // For simplicity, we'll just increment the likes count
      const { data, error } = await supabase.rpc('increment_comment_likes', {
        comment_id: commentId
      });

      if (error) {
        throw new Error(error.message);
      }

      // Update the comments list
      set(state => ({
        comments: state.comments.map(comment =>
          comment.id === commentId
            ? { ...comment, likes: comment.likes + 1, isLiked: true }
            : comment
        ),
        error: null
      }));

      return true;
    } catch (error) {
      console.error('Error liking comment:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  unlikeComment: async (commentId: string, isAnonymous = false) => {
    const { user } = useAuthStore.getState();

    if (!user && !isAnonymous) {
      set({ error: 'You must be logged in to unlike a comment' });
      return false;
    }

    try {
      // In a real app, we would use a junction table for likes
      // For simplicity, we'll just decrement the likes count
      const { data, error } = await supabase.rpc('decrement_comment_likes', {
        comment_id: commentId
      });

      if (error) {
        throw new Error(error.message);
      }

      // Update the comments list
      set(state => ({
        comments: state.comments.map(comment =>
          comment.id === commentId
            ? { ...comment, likes: Math.max(0, comment.likes - 1), isLiked: false }
            : comment
        ),
        error: null
      }));

      return true;
    } catch (error) {
      console.error('Error unliking comment:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return false;
    }
  },

  addReply: async (commentId: string, content: string, anonymousName?: string) => {
    const { user } = useAuthStore.getState();

    if (!content.trim()) {
      set({ error: 'Reply cannot be empty' });
      return null;
    }

    try {
      let data;
      let error;

      if (user) {
        // For authenticated users
        const result = await supabase
          .from('comment_replies')
          .insert({
            comment_id: commentId,
            user_id: user.id,
            user_name: user.email?.split('@')[0] || 'Anonymous',
            user_avatar: user.avatar || null,
            content: content.trim(),
            likes: 0,
            is_liked: false
          })
          .select()
          .single();

        data = result.data;
        error = result.error;
      } else {
        // For anonymous users
        if (!anonymousName || !anonymousName.trim()) {
          set({ error: 'Please provide a display name' });
          return null;
        }

        const result = await supabase
          .from('comment_replies')
          .insert({
            comment_id: commentId,
            user_id: 'anonymous',
            user_name: anonymousName.trim(),
            user_avatar: null,
            content: content.trim(),
            likes: 0,
            is_liked: false
          })
          .select()
          .single();

        data = result.data;
        error = result.error;
      }

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Comment type
      const newReply: Comment = {
        id: data.id,
        videoId: '', // Not needed for replies
        userId: data.user_id,
        userName: data.user_name || 'Anonymous',
        userAvatar: data.user_avatar || 'https://placehold.co/150/gray/white?text=User',
        content: data.content,
        createdAt: data.created_at,
        likes: data.likes || 0,
        isLiked: data.is_liked || false
      };

      // Update the comments list
      set(state => ({
        comments: state.comments.map(comment =>
          comment.id === commentId
            ? {
                ...comment,
                replies: [...(comment.replies || []), newReply]
              }
            : comment
        ),
        error: null
      }));

      return newReply;
    } catch (error) {
      console.error('Error adding reply:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      return null;
    }
  }
}));
