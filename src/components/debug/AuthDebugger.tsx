import React, { useState } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';

interface AuthDebuggerProps {
  visible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const AuthDebugger: React.FC<AuthDebuggerProps> = ({ 
  visible = false, 
  position = 'bottom-left' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { user, profile, isLoading, isApproved, loadUser } = useAuthStore();

  if (!visible) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  const handleRefreshAuth = async () => {
    setIsRefreshing(true);
    try {
      await loadUser();
    } catch (error) {
      console.error('Error refreshing auth:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleCheckSession = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    console.log('Current session:', session);
  };

  const handleCheckProfile = async () => {
    if (!user?.id) {
      console.log('No user ID available');
      return;
    }
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    console.log('Direct profile check:', { profile, error });
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <div className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-w-sm">
        {/* Header */}
        <div 
          className="flex items-center justify-between p-3 cursor-pointer bg-gray-700 rounded-t-lg"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span className="text-white text-sm font-medium">Auth Debug</span>
          <span className="text-white text-xs">
            {isExpanded ? '▼' : '▶'}
          </span>
        </div>

        {/* Content */}
        {isExpanded && (
          <div className="p-3 space-y-3">
            {/* Status Indicators */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isLoading ? 'bg-yellow-500' : 'bg-gray-500'}`}></div>
                <span className="text-white text-xs">Loading: {isLoading ? 'Yes' : 'No'}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${user ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-white text-xs">User: {user ? 'Signed In' : 'Not Signed In'}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${profile ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-white text-xs">Profile: {profile ? 'Exists' : 'Missing'}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isApproved ? 'bg-green-500' : 'bg-orange-500'}`}></div>
                <span className="text-white text-xs">Approved: {isApproved ? 'Yes' : 'No'}</span>
              </div>
            </div>

            {/* User Info */}
            {user && (
              <div className="border-t border-gray-600 pt-2">
                <div className="text-white text-xs space-y-1">
                  <div><strong>ID:</strong> {user.id}</div>
                  <div><strong>Email:</strong> {user.email}</div>
                  {profile && (
                    <>
                      <div><strong>Username:</strong> {profile.username}</div>
                      <div><strong>Approved:</strong> {profile.is_approved ? 'Yes' : 'No'}</div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="border-t border-gray-600 pt-2 space-y-2">
              <button
                onClick={handleRefreshAuth}
                disabled={isRefreshing}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white text-xs py-1 px-2 rounded"
              >
                {isRefreshing ? 'Refreshing...' : 'Refresh Auth'}
              </button>
              
              <button
                onClick={handleCheckSession}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white text-xs py-1 px-2 rounded"
              >
                Check Session
              </button>
              
              <button
                onClick={handleCheckProfile}
                disabled={!user?.id}
                className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white text-xs py-1 px-2 rounded"
              >
                Check Profile
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthDebugger;
