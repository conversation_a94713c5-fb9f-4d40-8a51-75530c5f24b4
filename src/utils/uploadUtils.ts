/**
 * Upload utilities for handling large files and mobile-specific optimizations
 */

import { supabase } from '../lib/supabase';
import { useAuthStore } from '../stores/authStore';

interface ChunkUploadOptions {
  chunkSize?: number;
  maxRetries?: number;
  onProgress?: (progress: number) => void;
  onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
}

/**
 * Check if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * Get optimized upload settings based on device type and connection
 */
export const getOptimizedUploadSettings = () => {
  const isMobile = isMobileDevice();
  const isAndroid = /Android/i.test(navigator.userAgent);
  
  // Estimate connection speed (rough approximation)
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  const isSlowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');

  return {
    chunkSize: isMobile ? (isSlowConnection ? 512 * 1024 : 1024 * 1024) : 2 * 1024 * 1024, // 512KB-2MB chunks
    maxRetries: isMobile ? 5 : 3,
    timeout: isMobile ? 60000 : 30000, // 60s for mobile, 30s for desktop
    maxConcurrentChunks: isMobile ? 1 : 3, // Sequential uploads on mobile
    compressionQuality: isMobile ? 0.7 : 0.8,
    maxFileSize: isMobile ? 60 * 1024 * 1024 : 100 * 1024 * 1024, // 60MB mobile, 100MB desktop
  };
};

/**
 * Upload a file in chunks with retry logic and progress tracking
 */
export const uploadFileInChunks = async (
  file: File,
  path: string,
  onProgress?: (progress: number) => void,
  options: ChunkUploadOptions = {}
): Promise<{ publicUrl: string }> => {
  const settings = getOptimizedUploadSettings();
  const {
    chunkSize = settings.chunkSize,
    maxRetries = settings.maxRetries,
    onChunkComplete
  } = options;

  // Check authentication before starting
  const { user } = useAuthStore.getState();
  if (!user) {
    throw new Error('Authentication required for upload');
  }

  const totalChunks = Math.ceil(file.size / chunkSize);
  let uploadedChunks = 0;

  console.log(`Starting chunked upload: ${totalChunks} chunks of ${chunkSize} bytes each`);

  // For small files, use regular upload
  if (file.size <= chunkSize) {
    const { error, data } = await supabase.storage
      .from('videos')
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      throw new Error(`Upload failed: ${error.message}`);
    }

    const { data: urlData } = supabase.storage
      .from('videos')
      .getPublicUrl(path);

    return urlData;
  }

  // For large files, implement chunked upload simulation
  // Note: Supabase doesn't natively support chunked uploads, so we'll use a different approach
  // We'll compress the file if it's too large and upload it normally with better error handling
  
  let processedFile = file;
  
  // If file is too large for mobile, we should compress it
  if (isMobileDevice() && file.size > settings.maxFileSize) {
    console.log('File too large for mobile, attempting compression...');
    processedFile = await compressVideoFile(file);
  }

  // Upload with retry logic
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Check auth state before each attempt
      const { user: currentUser } = useAuthStore.getState();
      if (!currentUser) {
        throw new Error('Authentication lost during upload');
      }

      console.log(`Upload attempt ${attempt}/${maxRetries}`);
      
      const { error, data } = await supabase.storage
        .from('videos')
        .upload(path, processedFile, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        lastError = new Error(`Upload failed: ${error.message}`);
        
        // If it's a network error, retry
        if (error.message.includes('network') || error.message.includes('timeout') || error.message.includes('fetch')) {
          console.log(`Network error on attempt ${attempt}, retrying...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 2000)); // Exponential backoff
          continue;
        }
        
        // If it's an auth error, don't retry
        if (error.message.includes('permission') || error.message.includes('unauthorized')) {
          throw lastError;
        }
        
        // For other errors, retry with backoff
        if (attempt < maxRetries) {
          console.log(`Error on attempt ${attempt}, retrying...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }
        
        throw lastError;
      }

      // Success!
      onProgress?.(1.0);
      
      const { data: urlData } = supabase.storage
        .from('videos')
        .getPublicUrl(path);

      return urlData;
      
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt < maxRetries) {
        console.log(`Attempt ${attempt} failed, retrying...`, error);
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  throw lastError || new Error('Upload failed after all retry attempts');
};

/**
 * Compress video file for mobile devices (placeholder implementation)
 */
export const compressVideoFile = async (file: File): Promise<File> => {
  // This is a placeholder - in a real implementation, you would:
  // 1. Use a library like FFmpeg.wasm to compress the video
  // 2. Or send it to a server-side compression service
  // 3. Or use browser APIs to reduce quality
  
  console.log('Video compression not implemented yet, returning original file');
  return file;
};

/**
 * Check network connectivity and speed
 */
export const checkNetworkConditions = (): {
  isOnline: boolean;
  connectionType: string;
  isSlowConnection: boolean;
} => {
  const isOnline = navigator.onLine;
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  const connectionType = connection?.effectiveType || 'unknown';
  const isSlowConnection = connectionType === 'slow-2g' || connectionType === '2g';
  
  return {
    isOnline,
    connectionType,
    isSlowConnection
  };
};

/**
 * Validate file before upload with mobile-specific checks
 */
export const validateFileForUpload = (file: File): { isValid: boolean; error?: string } => {
  const settings = getOptimizedUploadSettings();
  const networkConditions = checkNetworkConditions();
  
  if (!networkConditions.isOnline) {
    return {
      isValid: false,
      error: 'No internet connection. Please check your network and try again.'
    };
  }
  
  if (file.size > settings.maxFileSize) {
    const maxSizeMB = Math.round(settings.maxFileSize / (1024 * 1024));
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    return {
      isValid: false,
      error: `File is too large. Maximum size is ${maxSizeMB}MB. Your file is ${fileSizeMB}MB.`
    };
  }
  
  if (networkConditions.isSlowConnection && file.size > 10 * 1024 * 1024) {
    return {
      isValid: false,
      error: 'File is too large for your current connection speed. Please try with a smaller file or better connection.'
    };
  }
  
  return { isValid: true };
};

/**
 * Monitor upload progress and handle interruptions
 */
export const createUploadMonitor = (onProgress?: (progress: number) => void) => {
  let isAborted = false;
  
  const abort = () => {
    isAborted = true;
  };
  
  const checkAborted = () => {
    if (isAborted) {
      throw new Error('Upload was cancelled');
    }
  };
  
  return {
    abort,
    checkAborted,
    updateProgress: (progress: number) => {
      checkAborted();
      onProgress?.(progress);
    }
  };
};
