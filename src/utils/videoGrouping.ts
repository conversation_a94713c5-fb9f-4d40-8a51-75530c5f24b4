import { Video } from '../types';

/**
 * Interface representing a video series
 */
export interface VideoSeries {
  baseTitle: string;
  videos: Video[];
  totalVideos: number;
}

/**
 * Regular expressions for detecting numbered videos in different formats
 */
const NUMBERING_PATTERNS = [
  // Format: "Title - Part X" or "Title - Episode X" or "Title - #X" or "Title - S01E01"
  /^(.*?)(?:\s*[-:]\s*(?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,

  // Format: "Title (Part X)" or "Title (Episode X)" or "Title (#X)" or "Title (S01E01)"
  /^(.*?)(?:\s*\((?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+)\))(?:\s*[-:]\s*(.*))?$/i,

  // Format: "Title X" where X is a number at the end
  /^(.*?)\s+(\d+)$/i,

  // Format: "X. Title" or "X - Title" where X is a number at the beginning
  /^(\d+)(?:\.|\s*-\s*)\s*(.*)$/i,

  // Format: "Title: X" where X is a subtitle with a number
  /^(.*?)(?::\s*)(.*)(?:\s+)(\d+)(?:\s*[-:]\s*(.*))?$/i,

  // Format: "Title [X]" or "Title {X}" where X is a number in brackets
  /^(.*?)(?:\s*[\[\{]\s*(?:Part|Episode|Ep\.?|#)?\s*(\d+)\s*[\]\}])(?:\s*[-:]\s*(.*))?$/i,

  // Format: "Title Season X Episode Y" or "Title S01E01"
  /^(.*?)(?:\s+(?:Season|S)\s*(\d+)\s*(?:Episode|Ep\.?|E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,
  /^(.*?)(?:\s+S(\d+)E(\d+))(?:\s*[-:]\s*(.*))?$/i
];

/**
 * Extracts the base title and part number from a video title
 * @param title The video title to parse
 * @returns An object with baseTitle and partNumber, or null if no pattern is matched
 */
export function parseVideoTitle(title: string): { baseTitle: string; partNumber: number } | null {
  // Clean the title first - remove extra spaces, normalize dashes, etc.
  const cleanedTitle = title.replace(/\s+/g, ' ').trim();

  for (let i = 0; i < NUMBERING_PATTERNS.length; i++) {
    const pattern = NUMBERING_PATTERNS[i];
    const match = cleanedTitle.match(pattern);

    if (match) {
      // Handle different pattern types
      if (i === 0 || i === 1 || i === 5) {
        // "Title - Part X", "Title (Part X)", or "Title [X]"
        const baseTitle = match[1].trim();
        const partNumber = parseInt(match[2], 10);
        return { baseTitle, partNumber };
      } else if (i === 2) {
        // "Title X"
        const baseTitle = match[1].trim();
        const partNumber = parseInt(match[2], 10);
        return { baseTitle, partNumber };
      } else if (i === 3) {
        // "X. Title" or "X - Title"
        const baseTitle = match[2].trim();
        const partNumber = parseInt(match[1], 10);
        return { baseTitle, partNumber };
      } else if (i === 4) {
        // "Title: Subtitle X"
        const baseTitle = match[1].trim();
        const partNumber = parseInt(match[3], 10);
        return { baseTitle, partNumber };
      } else if (i === 6 || i === 7) {
        // "Title Season X Episode Y" or "Title S01E01"
        const baseTitle = match[1].trim();
        // Use episode number as part number, or combine season and episode
        const seasonNum = parseInt(match[2], 10);
        const episodeNum = parseInt(match[3] || '0', 10);
        const partNumber = episodeNum > 0 ? episodeNum : seasonNum;
        return { baseTitle, partNumber };
      }
    }
  }

  // If no pattern matched, check if the title itself is just a number
  // This helps with videos that are simply numbered without any text
  const numberMatch = cleanedTitle.match(/^(\d+)$/);
  if (numberMatch) {
    return { baseTitle: "Numbered Series", partNumber: parseInt(numberMatch[1], 10) };
  }

  return null;
}

/**
 * Normalizes a title for better comparison
 * Removes common words, punctuation, and normalizes spacing
 */
function normalizeTitle(title: string): string {
  // Convert to lowercase
  let normalized = title.toLowerCase();

  // Remove common words that don't affect meaning
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'of'];
  commonWords.forEach(word => {
    normalized = normalized.replace(new RegExp(`\\b${word}\\b`, 'g'), '');
  });

  // Remove punctuation and normalize spaces
  normalized = normalized
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .replace(/\s+/g, ' ')    // Normalize spaces
    .trim();

  return normalized;
}

/**
 * Checks if two titles belong to the same series
 * @param title1 First title
 * @param title2 Second title
 * @returns True if titles appear to be from the same series
 */
export function areTitlesFromSameSeries(title1: string, title2: string): boolean {
  const parsed1 = parseVideoTitle(title1);
  const parsed2 = parseVideoTitle(title2);

  if (!parsed1 || !parsed2) return false;

  // First try exact match after normalization
  const normalized1 = normalizeTitle(parsed1.baseTitle);
  const normalized2 = normalizeTitle(parsed2.baseTitle);

  if (normalized1 === normalized2) return true;

  // If not exact match, try similarity
  // Use a lower threshold (0.7) to catch more similar titles
  return areStringsSimilar(normalized1, normalized2, 0.7);
}

/**
 * Calculates string similarity using Levenshtein distance
 * @param str1 First string
 * @param str2 Second string
 * @param threshold Similarity threshold (0-1)
 * @returns True if strings are similar above the threshold
 */
function areStringsSimilar(str1: string, str2: string, threshold: number): boolean {
  // For very short strings, require higher similarity
  if (str1.length < 5 || str2.length < 5) {
    // For very short strings, use a higher threshold
    const shortDistance = levenshteinDistance(str1, str2);
    const shortMaxLength = Math.max(str1.length, str2.length);
    const shortSimilarity = 1 - shortDistance / shortMaxLength;
    return shortSimilarity >= 0.9; // Higher threshold for short strings
  }

  // Check if one string contains the other
  if (str1.includes(str2) || str2.includes(str1)) {
    return true;
  }

  // Calculate Levenshtein distance
  const distance = levenshteinDistance(str1, str2);
  const maxLength = Math.max(str1.length, str2.length);
  const similarity = 1 - distance / maxLength;

  return similarity >= threshold;
}

/**
 * Calculates Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const m = str1.length;
  const n = str2.length;

  // Create a matrix of size (m+1) x (n+1)
  const dp: number[][] = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));

  // Initialize the matrix
  for (let i = 0; i <= m; i++) {
    dp[i][0] = i;
  }

  for (let j = 0; j <= n; j++) {
    dp[0][j] = j;
  }

  // Fill the matrix
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      dp[i][j] = Math.min(
        dp[i - 1][j] + 1,      // deletion
        dp[i][j - 1] + 1,      // insertion
        dp[i - 1][j - 1] + cost // substitution
      );
    }
  }

  return dp[m][n];
}

/**
 * Groups videos that appear to be part of the same series
 * @param videos Array of videos to group
 * @returns Array of video series and standalone videos
 */
export function groupVideosBySeries(videos: Video[]): (Video | VideoSeries)[] {
  if (!videos || videos.length === 0) return [];

  // Create a map to store series by base title
  const seriesMap = new Map<string, Video[]>();
  const standaloneVideos: Video[] = [];

  // First pass: identify videos with numbered patterns
  const numberedVideos: Video[] = [];
  const unnumberedVideos: Video[] = [];

  for (const video of videos) {
    const parsedTitle = parseVideoTitle(video.title);
    if (parsedTitle) {
      numberedVideos.push(video);
    } else {
      unnumberedVideos.push(video);
    }
  }

  // Second pass: group numbered videos by similar base titles
  for (const video of numberedVideos) {
    const parsedTitle = parseVideoTitle(video.title)!; // We know this exists from first pass

    // Check if we already have a similar base title
    let foundMatch = false;

    for (const [baseTitle, seriesVideos] of seriesMap.entries()) {
      // Use normalized title comparison with a lower threshold
      if (areStringsSimilar(
        normalizeTitle(parsedTitle.baseTitle),
        normalizeTitle(baseTitle),
        0.7
      )) {
        seriesVideos.push(video);
        foundMatch = true;
        break;
      }
    }

    if (!foundMatch) {
      // Start a new series
      seriesMap.set(parsedTitle.baseTitle, [video]);
    }
  }

  // Third pass: try to match unnumbered videos with existing series
  // This helps group videos that don't have explicit numbering but belong to a series
  for (const video of unnumberedVideos) {
    let foundMatch = false;

    // Try to match with existing series by title similarity
    for (const [baseTitle, seriesVideos] of seriesMap.entries()) {
      // Check if this unnumbered video might belong to a series
      // Use a more aggressive similarity check
      const normalizedVideoTitle = normalizeTitle(video.title);
      const normalizedBaseTitle = normalizeTitle(baseTitle);

      if (normalizedVideoTitle.includes(normalizedBaseTitle) ||
          normalizedBaseTitle.includes(normalizedVideoTitle) ||
          areStringsSimilar(normalizedVideoTitle, normalizedBaseTitle, 0.6)) {
        seriesVideos.push(video);
        foundMatch = true;
        break;
      }
    }

    if (!foundMatch) {
      // Not part of any series
      standaloneVideos.push(video);
    }
  }

  // Fourth pass: check for exact title matches among standalone videos
  // This helps group videos with identical titles that don't have numbering
  const exactTitleMap = new Map<string, Video[]>();

  for (const video of standaloneVideos) {
    const normalizedTitle = normalizeTitle(video.title);

    if (exactTitleMap.has(normalizedTitle)) {
      exactTitleMap.get(normalizedTitle)!.push(video);
    } else {
      exactTitleMap.set(normalizedTitle, [video]);
    }
  }

  // Convert maps to array of series and sort videos within each series
  const result: (Video | VideoSeries)[] = [];

  // Add numbered series first
  for (const [baseTitle, seriesVideos] of seriesMap.entries()) {
    // Only create a series if there's more than one video
    if (seriesVideos.length > 1) {
      // Sort videos by part number if available, otherwise by title
      seriesVideos.sort((a, b) => {
        const parsedA = parseVideoTitle(a.title);
        const parsedB = parseVideoTitle(b.title);

        if (parsedA && parsedB) {
          return parsedA.partNumber - parsedB.partNumber;
        } else if (parsedA) {
          return -1; // Numbered videos come first
        } else if (parsedB) {
          return 1;  // Numbered videos come first
        }

        // If neither has a part number, sort by title
        return a.title.localeCompare(b.title);
      });

      result.push({
        baseTitle,
        videos: seriesVideos,
        totalVideos: seriesVideos.length
      });
    } else {
      // If only one video in the "series", treat it as standalone
      result.push(seriesVideos[0]);
    }
  }

  // Add exact title matches as series
  for (const [normalizedTitle, matchedVideos] of exactTitleMap.entries()) {
    if (matchedVideos.length > 1) {
      // Create a series for videos with identical titles
      result.push({
        baseTitle: matchedVideos[0].title, // Use the first video's title as base
        videos: matchedVideos,
        totalVideos: matchedVideos.length
      });
    } else {
      // Single videos go directly to result
      result.push(matchedVideos[0]);
    }
  }

  return result;
}
