/**
 * Utility functions for generating thumbnails from videos
 */

import { useAuthStore } from '../stores/authStore';

/**
 * Check if device has sufficient memory for thumbnail generation
 */
const checkMemoryAvailability = (): boolean => {
  // Check if we're on a mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  if (!isMobile) return true;

  // For mobile devices, check memory if available
  const memory = (navigator as any).deviceMemory;
  if (memory && memory < 2) {
    console.warn('Low device memory detected, thumbnail generation may be limited');
    return false;
  }

  return true;
};

/**
 * Protect authentication state during intensive operations
 */
const protectAuthState = () => {
  const { user } = useAuthStore.getState();
  if (!user) {
    throw new Error('Authentication required for thumbnail generation');
  }
  return user;
};

/**
 * Generate a thumbnail from a video file
 * @param videoFile The video file to generate a thumbnail from
 * @param timeInSeconds The time in seconds to capture the thumbnail (default: 0 for first frame)
 * @returns A Promise that resolves to a Blob containing the thumbnail image
 */
export const generateThumbnail = async (
  videoFile: File,
  timeInSeconds: number = 0
): Promise<Blob> => {
  // Check authentication state before starting
  try {
    protectAuthState();
  } catch (error) {
    throw error;
  }

  // Check memory availability
  if (!checkMemoryAvailability()) {
    throw new Error('Insufficient device memory for thumbnail generation. Please try uploading without a custom thumbnail.');
  }

  return new Promise((resolve, reject) => {
    // Check if we're on Android for special handling
    const isAndroid = /Android/i.test(navigator.userAgent);
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // Create a video element to load the video
    const video = document.createElement('video');
    video.preload = 'metadata';
    video.muted = true;
    video.playsInline = true;
    video.crossOrigin = 'anonymous';

    // Android-specific settings
    if (isAndroid) {
      video.controls = false;
      video.autoplay = false;
      // Set smaller dimensions for Android to avoid memory issues
      video.style.maxWidth = '320px';
      video.style.maxHeight = '240px';
    }

    // Create a URL for the video file with error handling
    let videoUrl: string;
    try {
      videoUrl = URL.createObjectURL(videoFile);
      video.src = videoUrl;
    } catch (error) {
      reject(new Error('Failed to create video URL: ' + error));
      return;
    }

    // Set up timeout for Android devices (they can be slower)
    const timeout = setTimeout(() => {
      URL.revokeObjectURL(videoUrl);
      reject(new Error('Thumbnail generation timed out'));
    }, isAndroid ? 15000 : 10000);

    // Set up event handlers
    video.onloadedmetadata = () => {
      try {
        // Check auth state again during processing
        const { user } = useAuthStore.getState();
        if (!user) {
          clearTimeout(timeout);
          URL.revokeObjectURL(videoUrl);
          reject(new Error('Authentication lost during thumbnail generation'));
          return;
        }

        // If the requested time is beyond the video duration, use the middle of the video
        if (timeInSeconds > video.duration) {
          timeInSeconds = video.duration / 2;
        }

        // Seek to the specified time
        video.currentTime = timeInSeconds;
      } catch (error) {
        clearTimeout(timeout);
        URL.revokeObjectURL(videoUrl);
        reject(new Error('Failed to seek video: ' + error));
      }
    };

    video.onseeked = () => {
      try {
        clearTimeout(timeout);

        // Check auth state again before canvas operations
        const { user } = useAuthStore.getState();
        if (!user) {
          URL.revokeObjectURL(videoUrl);
          reject(new Error('Authentication lost during thumbnail generation'));
          return;
        }

        // Create a canvas element to draw the video frame
        const canvas = document.createElement('canvas');

        // Use actual video dimensions or fallback for Android
        const width = video.videoWidth || 640;
        const height = video.videoHeight || 480;

        // Limit canvas size on Android to prevent memory issues
        if (isAndroid) {
          const maxDimension = 800;
          const aspectRatio = width / height;

          if (width > height) {
            canvas.width = Math.min(width, maxDimension);
            canvas.height = canvas.width / aspectRatio;
          } else {
            canvas.height = Math.min(height, maxDimension);
            canvas.width = canvas.height * aspectRatio;
          }
        } else {
          canvas.width = width;
          canvas.height = height;
        }

        // Draw the current frame to the canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          URL.revokeObjectURL(videoUrl);
          reject(new Error('Failed to get canvas context'));
          return;
        }

        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert the canvas to a blob with Android-optimized settings
        const quality = isAndroid ? 0.7 : 0.8; // Lower quality for Android
        canvas.toBlob((blob) => {
          if (blob) {
            // Clean up
            URL.revokeObjectURL(videoUrl);
            resolve(blob);
          } else {
            URL.revokeObjectURL(videoUrl);
            reject(new Error('Failed to generate thumbnail'));
          }
        }, 'image/jpeg', quality);
      } catch (error) {
        clearTimeout(timeout);
        URL.revokeObjectURL(videoUrl);
        reject(new Error('Error during thumbnail generation: ' + error));
      }
    };

    video.onerror = (error) => {
      clearTimeout(timeout);
      URL.revokeObjectURL(videoUrl);
      reject(new Error('Error loading video: ' + error));
    };

    // Start loading the video with error handling
    try {
      video.load();
    } catch (error) {
      clearTimeout(timeout);
      URL.revokeObjectURL(videoUrl);
      reject(new Error('Failed to load video: ' + error));
    }
  });
};

/**
 * Generate multiple thumbnails from a video file at different timestamps
 * @param videoFile The video file to generate thumbnails from
 * @param count The number of thumbnails to generate (default: 3)
 * @returns A Promise that resolves to an array of Blobs containing the thumbnail images
 */
export const generateMultipleThumbnails = async (
  videoFile: File,
  count: number = 3
): Promise<Blob[]> => {
  return new Promise((resolve, reject) => {
    // Create a video element to load the video
    const video = document.createElement('video');
    video.preload = 'metadata';
    video.muted = true;

    // Create a URL for the video file
    const videoUrl = URL.createObjectURL(videoFile);
    video.src = videoUrl;

    // Set up event handlers
    video.onloadedmetadata = async () => {
      try {
        const thumbnails: Blob[] = [];
        const duration = video.duration;

        // Generate thumbnails at evenly spaced intervals
        for (let i = 0; i < count; i++) {
          const timeInSeconds = (duration * (i + 1)) / (count + 1);
          const thumbnail = await generateThumbnail(videoFile, timeInSeconds);
          thumbnails.push(thumbnail);
        }

        // Clean up
        URL.revokeObjectURL(videoUrl);
        resolve(thumbnails);
      } catch (error) {
        URL.revokeObjectURL(videoUrl);
        reject(error);
      }
    };

    video.onerror = () => {
      URL.revokeObjectURL(videoUrl);
      reject(new Error('Error loading video'));
    };

    // Start loading the video
    video.load();
  });
};

/**
 * Convert a Blob to a File object
 * @param blob The Blob to convert
 * @param filename The name for the file
 * @returns A File object
 */
export const blobToFile = (blob: Blob, filename: string): File => {
  return new File([blob], filename, { type: blob.type });
};
