import useSWR, { SWRConfiguration } from 'swr';
import { supabase } from '../lib/supabase';
import { Video } from '../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../utils/mediaUtils';

// Define the fetcher function using direct fetch (bypassing Supabase client timeout issues)
const videoFetcher = async (key: string) => {
  console.log('🔍 videoFetcher called with key:', key);

  try {
    // Parse the key to extract parameters
    const [_, category, page, pageSize] = key.split('/');
    // console.log('📊 Parsed parameters:', { category, page, pageSize });

    // Use direct fetch instead of Supabase client to avoid timeout issues
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing environment variables');
    }

    // Calculate range for pagination
    const currentPage = parseInt(page) || 1;
    const itemsPerPage = parseInt(pageSize) || 12;
    const from = (currentPage - 1) * itemsPerPage;
    const to = from + itemsPerPage - 1;
    // console.log('📄 Pagination:', { currentPage, itemsPerPage, from, to });

    // Use direct fetch to avoid Supabase client timeout issues
    // console.log('🔗 Using direct fetch to bypass timeout issues...');

    // Build query parameters
    const params = new URLSearchParams({
      select: 'id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags',
      limit: itemsPerPage.toString(),
      offset: from.toString()
    });

    // Set ordering based on category
    if (category === 'recommended') {
      // For recommended, show most viewed videos first
      params.append('order', 'views.desc');
    } else {
      // For all other categories, show newest first
      params.append('order', 'created_at.desc');
    }

    // Add category filter if specified (but not for recommended, which shows all videos by view count)
    if (category && category !== 'all' && category !== '' && category !== 'recommended') {
      params.append('category', `eq.${category}`);
    }

    // console.log('🔗 Direct fetch URL:', `${supabaseUrl}/rest/v1/videos?${params}`);

    // Use direct fetch with shorter timeout
    const response = await fetch(`${supabaseUrl}/rest/v1/videos?${params}`, {
      method: 'GET',
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const videoData = await response.json();
    // console.log('🔢 Direct fetch result:', { dataLength: videoData?.length });

    if (!Array.isArray(videoData)) {
      throw new Error('Invalid response format');
    }

    // console.log('✅ Video fetch completed successfully, transforming data...');

    // Get the actual total count from the database
    let totalCount = 0;
    try {
      // Make a separate request to get the total count
      const countParams = new URLSearchParams({
        select: 'count'
      });

      // Add category filter if specified (but not for recommended, which shows all videos)
      if (category && category !== 'all' && category !== '' && category !== 'recommended') {
        countParams.append('category', `eq.${category}`);
      }

      const countResponse = await fetch(`${supabaseUrl}/rest/v1/videos?${countParams}`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Prefer': 'count=exact'
        },
        signal: AbortSignal.timeout(5000)
      });

      if (countResponse.ok) {
        const countHeader = countResponse.headers.get('content-range');
        if (countHeader) {
          // Parse "0-23/187" format to get total count
          const match = countHeader.match(/\/(\d+)$/);
          if (match) {
            totalCount = parseInt(match[1]);
          }
        }
      }
    } catch (error) {
      console.warn('Could not fetch total count, using current page length:', error);
      totalCount = videoData?.length || 0;
    }

    // If we couldn't get the total count, estimate it
    if (totalCount === 0) {
      totalCount = videoData?.length || 0;
    }

    const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

    // Transform the data to match our Video type
    const videos: Video[] = (videoData || []).map(item => ({
      id: item.id,
      title: item.title,
      description: item.description || '',
      thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
      videoUrl: getWorkingVideoUrl(item.video_url || ''),
      duration: item.duration || 0,
      views: item.views || 0,
      likes: item.likes || 0,
      createdAt: item.created_at,
      updatedAt: item.updated_at || item.created_at,
      publishedAt: item.created_at,
      scheduledFor: undefined,
      status: 'public',
      isHD: item.is_hd || false,
      isPremium: false,
      tags: Array.isArray(item.tags) ? item.tags : [],
      category: item.category || 'uncategorized',
      creator: {
        id: item.user_id || '',
        email: '',
        avatar: 'https://via.placeholder.com/150?text=User',
        isVerified: false,
        isCreator: true,
        subscriberCount: 0
      }
    }));

    const result = {
      videos,
      pagination: {
        currentPage,
        totalPages,
        totalCount,
        pageSize: itemsPerPage
      }
    };

    // console.log('🎯 videoFetcher returning:', {
    //   videosCount: videos.length,
    //   totalCount,
    //   totalPages,
    //   currentPage
    // });

    return result;
  } catch (error) {
    // console.error('❌ Error in videoFetcher:', error);
    throw error;
  }
};

// Define the hook
export function useVideos(
  category: string = '',
  page: number = 1,
  pageSize: number = 12,
  config?: SWRConfiguration
) {
  const { data, error, isLoading, isValidating, mutate } = useSWR(
    `videos/${category}/${page}/${pageSize}`,
    videoFetcher,
    {
      revalidateOnFocus: false,
      revalidateIfStale: true,
      revalidateOnReconnect: true,
      dedupingInterval: 5000,
      focusThrottleInterval: 10000,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      onError: (error) => {
        // console.error('🚨 SWR Error in useVideos:', error);
      },
      onSuccess: (data) => {
        // console.log('✅ SWR Success in useVideos:', { videosCount: data?.videos?.length });
      },
      ...config
    }
  );

  return {
    videos: data?.videos || [],
    pagination: data?.pagination || {
      currentPage: page,
      totalPages: 1,
      totalCount: 0,
      pageSize
    },
    isLoading,
    isValidating,
    error: error?.message,
    mutate
  };
}

// Hook for fetching a single video by ID
export function useVideo(id: string, config?: SWRConfiguration) {
  const fetcher = async () => {
    // console.log('🔍 useVideo fetcher called for ID:', id);

    // Use direct fetch to avoid Supabase client timeout issues
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing environment variables');
    }

    // Build query parameters for single video
    const params = new URLSearchParams({
      select: 'id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags',
      id: `eq.${id}`
    });

    console.log('🔗 Direct fetch single video URL:', `${supabaseUrl}/rest/v1/videos?${params}`);

    const response = await fetch(`${supabaseUrl}/rest/v1/videos?${params}`, {
      method: 'GET',
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const videoArray = await response.json();
    console.log('🔢 Direct fetch single video result:', { dataLength: videoArray?.length });

    if (!Array.isArray(videoArray) || videoArray.length === 0) {
      throw new Error('Video not found');
    }

    const data = videoArray[0]; // Get first (and should be only) result

    // Transform the data to match our Video type
    const video: Video = {
      id: data.id,
      title: data.title,
      description: data.description || '',
      thumbnailUrl: getWorkingThumbnailUrl(data.thumbnail_url || ''),
      videoUrl: getWorkingVideoUrl(data.video_url || ''),
      duration: data.duration || 0,
      views: data.views || 0,
      likes: data.likes || 0,
      createdAt: data.created_at,
      updatedAt: data.updated_at || data.created_at,
      publishedAt: data.created_at,
      scheduledFor: undefined,
      status: 'public',
      isHD: data.is_hd || false,
      isPremium: false,
      tags: Array.isArray(data.tags) ? data.tags : [],
      category: data.category || 'uncategorized',
      creator: {
        id: data.user_id || '',
        email: '',
        avatar: 'https://via.placeholder.com/150?text=User',
        isVerified: false,
        isCreator: true,
        subscriberCount: 0
      }
    };

    return video;
  };

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    id ? `video/${id}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      onError: (error) => {
        // console.error('🚨 SWR Error in useVideo:', error);
      },
      onSuccess: (data) => {
        // console.log('✅ SWR Success in useVideo:', { videoId: data?.id, title: data?.title });
      },
      ...config
    }
  );

  return {
    video: data,
    isLoading,
    isValidating,
    error: error?.message,
    mutate
  };
}
