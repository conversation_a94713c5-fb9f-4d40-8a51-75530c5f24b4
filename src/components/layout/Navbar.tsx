import React, { useState, useEffect, useRef } from 'react';
import { Bell, Menu, Upload, LogIn, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import SearchBar from '../search/SearchBar';
import { useAuthStore } from '../../stores/authStore';
import { getAnimatedAvatar, getFallbackAvatar } from '../../utils/avatarUtils';

interface NavbarProps {
  onOpenSidebar: () => void;
  isAuthenticated?: boolean;
  onLoginClick?: () => void;
  onSignUpClick?: () => void;
}

const Navbar: React.FC<NavbarProps> = ({
  onOpenSidebar,
  isAuthenticated = false,
  onLoginClick,
  onSignUpClick
}) => {
  const navigate = useNavigate();
  const { user, signOut } = useAuthStore();
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const searchBarRef = useRef<HTMLDivElement>(null);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Generate avatar based on user ID or a default seed
  const avatarUrl = user ? getAnimatedAvatar(user.id) : getAnimatedAvatar('guest');

  // Handle clicks outside the user menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = async () => {
    try {
      console.log('Logout button clicked');

      // Close the user menu immediately
      setShowUserMenu(false);

      // Sign out from auth store
      await signOut();

      // Navigate to home page
      navigate('/');

      console.log('Logout completed successfully');
    } catch (error) {
      console.error('Error signing out:', error);
      // Even if there's an error, close the menu and navigate home
      setShowUserMenu(false);
      navigate('/');
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Focus search input when mobile search is opened
  useEffect(() => {
    if (searchOpen && searchBarRef.current) {
      // Find the input element inside the search bar
      const inputElement = searchBarRef.current.querySelector('input');
      if (inputElement) {
        setTimeout(() => {
          inputElement.focus();
        }, 100);
      }
    }
  }, [searchOpen]);

  return (
    <header
      className={`
        fixed top-0 left-0 right-0 z-40 transition-all duration-300
        ${isScrolled ? 'bg-gray-900/95 backdrop-blur-sm shadow-md' : 'bg-gradient-to-b from-gray-900 to-transparent'}
      `}
    >
      <div className="container mx-auto px-4">
        {/* Desktop layout */}
        <div className="hidden md:flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center lg:ml-0">
            <button
              className="font-bold text-xl text-white hover:opacity-80 transition-opacity"
              onClick={() => navigate('/', { replace: true })}
            >
              <span className="text-blue-500">Blue</span>Film
            </button>
          </div>

          {/* Desktop Search Bar */}
          <div className="flex-1 max-w-xl mx-6">
            <div className="relative search-bar-container">
              <div className="absolute -top-6 left-4 text-blue-500 font-medium text-sm">Search Videos</div>
              <SearchBar className="w-full" />
            </div>
          </div>

          {/* Right side actions - Desktop */}
          <div className="flex items-center space-x-3">
            {isAuthenticated ? (
              <>
                <button className="p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white">
                  <Bell size={22} />
                </button>
                <button
                  className="p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white"
                  onClick={() => navigate('/upload')}
                  title="Upload Video"
                >
                  <Upload size={22} />
                </button>
                <div className="relative ml-2" ref={userMenuRef}>
                  <button
                    className="h-9 w-9 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                  >
                    <img
                      src={avatarUrl}
                      alt="Profile"
                      className="h-full w-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = getFallbackAvatar();
                      }}
                    />
                  </button>

                  {/* User dropdown menu */}
                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700">
                      <button
                        className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
                        onClick={() => navigate('/manage')}
                      >
                        My Videos
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
                        onClick={() => navigate('/favorites')}
                      >
                        Favorites
                      </button>
                      <div className="border-t border-gray-700 my-1"></div>
                      <button
                        className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2"
                        onClick={handleLogout}
                      >
                        <LogOut size={16} />
                        <span>Log Out</span>
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hidden md:inline-flex"
                  onClick={onLoginClick}
                >
                  Log in
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  leftIcon={<LogIn size={16} />}
                  onClick={onSignUpClick}
                >
                  Sign Up
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile layout */}
        <div className="md:hidden">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={onOpenSidebar}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-700"
                aria-expanded="false"
              >
                <span className="sr-only">Open main menu</span>
                <Menu className="block h-6 w-6" aria-hidden="true" />
              </button>

              {/* Logo */}
              <div className="flex-shrink-0 flex items-center ml-2">
                <button
                  className="font-bold text-lg text-white hover:opacity-80 transition-opacity"
                  onClick={() => navigate('/', { replace: true })}
                >
                  <span className="text-blue-500">Blue</span>Film
                </button>
              </div>
            </div>

            {/* Mobile search bar removed from here - now in HomePage */}

            {/* Right side actions - Mobile */}
            <div className="flex items-center">
              {isAuthenticated ? (
                <>
                  <button
                    className="p-1.5 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white"
                    onClick={() => navigate('/upload')}
                    title="Upload Video"
                  >
                    <Upload size={18} />
                  </button>
                  <div className="relative ml-1" ref={userMenuRef}>
                    <button
                      className="h-7 w-7 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100"
                      onClick={() => setShowUserMenu(!showUserMenu)}
                    >
                      <img
                        src={avatarUrl}
                        alt="Profile"
                        className="h-full w-full object-contain"
                        onError={(e) => {
                          e.currentTarget.src = getFallbackAvatar();
                        }}
                      />
                    </button>

                    {/* User dropdown menu */}
                    {showUserMenu && (
                      <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700">
                        <button
                          className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
                          onClick={() => navigate('/manage')}
                        >
                          My Videos
                        </button>
                        <button
                          className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
                          onClick={() => navigate('/favorites')}
                        >
                          Favorites
                        </button>
                        <div className="border-t border-gray-700 my-1"></div>
                        <button
                          className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2"
                          onClick={handleLogout}
                        >
                          <LogOut size={16} />
                          <span>Log Out</span>
                        </button>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <Button
                  variant="primary"
                  size="xs"
                  leftIcon={<LogIn size={14} />}
                  onClick={onSignUpClick}
                >
                  Sign Up
                </Button>
              )}
            </div>
          </div>
        </div>


      </div>
    </header>
  );
};

export default Navbar;