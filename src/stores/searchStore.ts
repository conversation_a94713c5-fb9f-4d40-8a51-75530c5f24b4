import { create } from 'zustand';
import { supabase, getVideoQuery } from '../lib/supabase';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../utils/mediaUtils';
import { Video } from '../types';

interface SearchState {
  searchQuery: string;
  searchResults: Video[];
  recentSearches: string[];
  isLoading: boolean;
  error: string | null;

  // Actions
  setSearchQuery: (query: string) => void;
  search: (query?: string) => Promise<void>;
  clearSearch: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
}

const MAX_RECENT_SEARCHES = 5;

export const useSearchStore = create<SearchState>((set, get) => ({
  searchQuery: '',
  searchResults: [],
  recentSearches: JSON.parse(localStorage.getItem('recentSearches') || '[]'),
  isLoading: false,
  error: null,

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  search: async (query?: string) => {
    const searchTerm = query || get().searchQuery;

    if (!searchTerm.trim()) {
      set({ searchResults: [], error: null });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      console.log('Searching for:', searchTerm);

      // Search in titles and descriptions only (removed tags search as it doesn't exist)
      // Make search case-insensitive and more flexible
      const { data, error } = await getVideoQuery(
        supabase.from('videos')
      )
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
        .order('views', { ascending: false }) // Order by views to show most popular first
        .limit(50); // Limit results for better performance

      if (error) {
        console.error('Search query error:', error);
        throw new Error(error.message);
      }

      console.log('Search results:', data?.length || 0, 'videos found');

      // Transform the data to match our Video type
      const videos: Video[] = data.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(item.video_url || ''),
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at,
        publishedAt: item.created_at, // Use created_at since published_at doesn't exist in schema
        scheduledFor: undefined, // Field doesn't exist in schema
        status: 'public', // Field doesn't exist in schema, default to public
        isHD: item.is_hd || false,
        isPremium: false, // Field doesn't exist in schema, default to false
        tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
        category: item.category || 'uncategorized',
        creator: {
          id: item.creator?.id || '',
          email: '',
          avatar: item.creator?.avatar_url || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      set({
        searchResults: videos,
        isLoading: false
      });

      // Add to recent searches if it's a new search
      if (searchTerm.trim() && query) {
        get().addRecentSearch(searchTerm);
      }
    } catch (error) {
      console.error('Search error:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  clearSearch: () => {
    set({ searchQuery: '', searchResults: [] });
  },

  addRecentSearch: (query: string) => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    const currentSearches = get().recentSearches;

    // Remove the query if it already exists
    const filteredSearches = currentSearches.filter(
      search => search.toLowerCase() !== trimmedQuery.toLowerCase()
    );

    // Add the new query to the beginning
    const newSearches = [trimmedQuery, ...filteredSearches].slice(0, MAX_RECENT_SEARCHES);

    // Update state and localStorage
    set({ recentSearches: newSearches });
    localStorage.setItem('recentSearches', JSON.stringify(newSearches));
  },

  clearRecentSearches: () => {
    set({ recentSearches: [] });
    localStorage.removeItem('recentSearches');
  }
}));
