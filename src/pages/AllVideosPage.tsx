import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import VideoGrid from '../components/sections/VideoGrid';
import VirtualizedVideoGrid from '../components/sections/VirtualizedVideoGrid';
import Pagination from '../components/ui/Pagination';
import QuickVideoFix from '../components/debug/QuickVideoFix';
import EmergencyVideoLoader from '../components/emergency/EmergencyVideoLoader';
import { useVideoStore } from '../stores/videoStore';
import { useVideos } from '../hooks/useVideos';
import { Video } from '../types';

const AllVideosPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [fallbackVideos, setFallbackVideos] = useState<Video[]>([]);
  const [useFallback, setUseFallback] = useState(false);
  const [emergencyVideos, setEmergencyVideos] = useState<Video[]>([]);
  const [useEmergency, setUseEmergency] = useState(false);
  const [quickFixAttempted, setQuickFixAttempted] = useState(false);

  // Use SWR hooks for data fetching with caching (same as homepage)
  const {
    videos: swrVideos,
    pagination: swrPagination,
    isLoading: swrLoading,
    error: swrError
  } = useVideos('', currentPage, 12); // Empty category means all videos

  // Show loading issue warning if stuck for too long
  const showLoadingIssue = swrLoading && swrVideos.length === 0 && !swrError && !useFallback && !useEmergency;

  // Determine which videos to display (priority: emergency > fallback > SWR)
  const displayVideos = useEmergency ? emergencyVideos : (useFallback ? fallbackVideos : swrVideos);
  const displayLoading = (useEmergency || useFallback) ? false : swrLoading;
  const displayError = (useEmergency || useFallback) ? null : swrError;
  const displayPagination = useFallback ? { currentPage, totalPages: 1, totalCount: fallbackVideos.length, pageSize: 12 } : swrPagination;

  // Show emergency loader if quick fix has been attempted but failed
  const showEmergencyLoader = showLoadingIssue && quickFixAttempted && !useFallback;

  // Handle fallback video loading
  const handleFallbackVideos = (loadedVideos: Video[]) => {
    setFallbackVideos(loadedVideos);
    setUseFallback(true);
    setQuickFixAttempted(true);
    // console.log('✅ Using fallback videos in AllVideosPage:', loadedVideos.length);
  };

  // Handle emergency video loading
  const handleEmergencyVideos = (loadedVideos: Video[]) => {
    setEmergencyVideos(loadedVideos);
    setUseEmergency(true);
    // console.log('🚨 Using emergency videos in AllVideosPage:', loadedVideos.length);
  };

  const handleVideoClick = (video: Video) => {
    setSelectedVideo(video.id);
    navigate(`/video/${video.id}`);
  };

  const handleBackClick = () => {
    navigate('/');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={handleBackClick}
          className="mr-4 p-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
          aria-label="Go back"
        >
          <ArrowLeft size={20} className="text-white" />
        </button>
        <h1 className="text-2xl font-bold text-white">
          All Videos
        </h1>
      </div>

      {/* Error Display */}
      {displayError && (
        <div className="bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6">
          <h3 className="font-bold mb-2">Error Loading Videos</h3>
          <p>{displayError}</p>
        </div>
      )}

      {/* Quick Video Fix - appears when SWR is stuck */}
      {false && (
        <QuickVideoFix
          onVideosLoaded={handleFallbackVideos}
          isVisible={showLoadingIssue}
        />
      )}

      {/* Emergency Video Loader - appears if quick fix fails */}
      {false && (
        <EmergencyVideoLoader
          onVideosLoaded={handleEmergencyVideos}
          isVisible={showEmergencyLoader}
        />
      )}

      {/* Loading Issue Warning - hidden in production */}
      {false && showLoadingIssue && !useFallback && !useEmergency && (
        <div className="bg-yellow-600 text-white p-4 rounded-lg mb-6">
          <h3 className="font-bold mb-2">⚠️ Loading Issue Detected</h3>
          <p className="mb-3">Videos are taking longer than expected to load. This might be due to:</p>
          <ul className="list-disc list-inside mb-3 text-sm">
            <li>Network connectivity issues</li>
            <li>Database connection problems</li>
            <li>Browser cache conflicts</li>
          </ul>
          <div className="flex gap-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-white text-yellow-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100"
            >
              Reload Page
            </button>
            <button
              onClick={() => {
                localStorage.clear();
                sessionStorage.clear();
                window.location.reload();
              }}
              className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-700"
            >
              Clear Cache & Reload
            </button>
          </div>
        </div>
      )}

      {/* Main Video Grid - Using Virtualization for better performance */}
      <VirtualizedVideoGrid
        title="All Videos"
        videos={displayVideos}
        onVideoClick={handleVideoClick}
        isLoading={displayLoading}
        gridColumns={4}
        itemHeight={320}
        overscanCount={3}
      />

      {/* Pagination */}
      {!displayLoading && displayVideos.length > 0 && (
        <div className="mt-8">
          <Pagination
            currentPage={displayPagination.currentPage}
            totalPages={displayPagination.totalPages}
            onPageChange={(page) => setCurrentPage(page)}
            className="mb-8"
          />
        </div>
      )}

      {!displayLoading && displayVideos.length === 0 && !showLoadingIssue && (
        <div className="text-center py-12">
          <h2 className="text-xl text-gray-400 mb-4">No videos found</h2>
          <button
            onClick={handleBackClick}
            className="px-6 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Back to Home
          </button>
        </div>
      )}
    </div>
  );
};

export default AllVideosPage;
