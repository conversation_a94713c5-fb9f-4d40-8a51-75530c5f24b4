import React, { useState } from 'react';
import { useVideoStore } from '../../stores/videoStore';
import { supabase } from '../../lib/supabase';

interface VideoDebugPanelProps {
  visible?: boolean;
}

const VideoDebugPanel: React.FC<VideoDebugPanelProps> = ({ visible = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { videos, isLoading: videosLoading, error } = useVideoStore();

  if (!visible) return null;

  const testVideoFetch = async () => {
    setIsLoading(true);
    setTestResults(null);

    try {
      // Test direct Supabase query
      const { data, error: queryError } = await supabase
        .from('videos')
        .select(`
          id,
          title,
          description,
          thumbnail_url,
          video_url,
          duration,
          views,
          likes,
          is_hd,
          user_id,
          created_at,
          updated_at,
          category,
          tags,
          creator:profiles(id, username, avatar_url)
        `)
        .limit(3);

      if (queryError) {
        setTestResults({
          success: false,
          error: queryError.message,
          data: null
        });
      } else {
        setTestResults({
          success: true,
          error: null,
          data: data,
          count: data?.length || 0
        });
      }
    } catch (err) {
      setTestResults({
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error',
        data: null
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testImageLoad = (url: string) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve({ success: true, url });
      img.onerror = () => resolve({ success: false, url });
      img.src = url;
    });
  };

  const testVideoLoad = (url: string) => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => resolve({ success: true, url, duration: video.duration });
      video.onerror = () => resolve({ success: false, url });
      video.src = url;
    });
  };

  return (
    <div className="fixed bottom-4 left-4 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs max-w-md z-50 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Video Debug Panel</h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-400 hover:text-blue-300"
        >
          {isExpanded ? 'Collapse' : 'Expand'}
        </button>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-gray-300">Videos in store:</span>
          <span className={videos.length > 0 ? 'text-green-400' : 'text-red-400'}>
            {videos.length}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-300">Store loading:</span>
          <span className={videosLoading ? 'text-yellow-400' : 'text-green-400'}>
            {videosLoading ? 'Yes' : 'No'}
          </span>
        </div>

        {error && (
          <div className="text-red-400">
            <span className="text-gray-300">Store error:</span> {error}
          </div>
        )}

        <button
          onClick={testVideoFetch}
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Video Fetch'}
        </button>

        {testResults && (
          <div className="mt-2 p-2 bg-gray-800 rounded">
            <div className="flex justify-between">
              <span>Fetch result:</span>
              <span className={testResults.success ? 'text-green-400' : 'text-red-400'}>
                {testResults.success ? 'Success' : 'Failed'}
              </span>
            </div>

            {testResults.error && (
              <div className="text-red-400 mt-1">
                Error: {testResults.error}
              </div>
            )}

            {testResults.success && (
              <div className="text-green-400 mt-1">
                Found {testResults.count} videos
              </div>
            )}
          </div>
        )}

        {isExpanded && (
          <div className="mt-4 space-y-2">
            <h4 className="font-semibold">Sample Videos:</h4>
            {videos.slice(0, 3).map((video, index) => (
              <div key={video.id} className="p-2 bg-gray-800 rounded">
                <div className="font-medium truncate">{video.title}</div>
                <div className="text-gray-400 text-xs">
                  <div>ID: {video.id}</div>
                  <div>Thumbnail: {video.thumbnailUrl ? '✓' : '✗'}</div>
                  <div>Video URL: {video.videoUrl ? '✓' : '✗'}</div>
                  {video.thumbnailUrl && (
                    <div className="mt-1">
                      <button
                        onClick={async () => {
                          const result = await testImageLoad(video.thumbnailUrl);
                          // console.log('Thumbnail test:', result);
                        }}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        Test Thumbnail
                      </button>
                    </div>
                  )}
                  {video.videoUrl && (
                    <div className="mt-1">
                      <button
                        onClick={async () => {
                          const result = await testVideoLoad(video.videoUrl);
                          console.log('Video test:', result);
                        }}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        Test Video
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoDebugPanel;
