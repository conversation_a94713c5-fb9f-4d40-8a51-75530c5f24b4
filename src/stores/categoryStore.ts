import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Category } from '../types';

interface CategoryState {
  categories: Category[];
  featuredCategories: Category[];
  topCategories: Category[];
  isLoading: boolean;
  error: string | null;

  // Fetch operations
  fetchCategories: () => Promise<void>;
  fetchFeaturedCategories: () => Promise<void>;
  fetchTopCategories: (limit?: number) => Promise<void>;

  // Category operations
  getCategoryBySlug: (slug: string) => Category | undefined;
}

export const useCategoryStore = create<CategoryState>((set, get) => ({
  categories: [],
  featuredCategories: [],
  topCategories: [],
  isLoading: false,
  error: null,

  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        throw new Error(error.message);
      }

      // Transform the data to match our Category type
      const categories: Category[] = data.map(item => ({
        id: item.id,
        name: item.name,
        slug: item.slug
      }));

      set({ categories, isLoading: false });
    } catch (error) {
      // console.error('Error fetching categories:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchFeaturedCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would fetch featured categories based on some criteria
      // For now, we'll just get the top 6 categories with the most videos
      const { data, error } = await supabase
        .from('videos')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        throw new Error(error.message);
      }

      // Count videos per category
      const categoryCounts: Record<string, number> = {};
      data.forEach(item => {
        if (item.category) {
          categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1;
        }
      });

      // Sort categories by count and get the top 6
      const topCategories = Object.entries(categoryCounts)
        .sort(([, countA], [, countB]) => countB - countA)
        .slice(0, 6)
        .map(([slug]) => slug);

      // Get the full category objects for these slugs
      const { data: featuredData, error: featuredError } = await supabase
        .from('categories')
        .select('*')
        .in('slug', topCategories);

      if (featuredError) {
        throw new Error(featuredError.message);
      }

      // Transform the data to match our Category type
      const featuredCategories: Category[] = featuredData.map(item => ({
        id: item.id,
        name: item.name,
        slug: item.slug
      }));

      set({ featuredCategories, isLoading: false });
    } catch (error) {
      // console.error('Error fetching featured categories:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  fetchTopCategories: async (limit = 6) => {
    set({ isLoading: true, error: null });
    try {
      // Define special categories that should always appear at the top
      const specialCategories = ['hot', 'trending', 'new'];

      // Get all categories from videos table
      const { data, error } = await supabase
        .from('videos')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        throw new Error(error.message);
      }

      // Count categories manually since Supabase doesn't support GROUP BY in the client
      const categoryCount: { [key: string]: number } = {};

      data.forEach(item => {
        if (item.category) {
          categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
        }
      });

      // Transform the data to match our Category type
      let allCategories: Category[] = Object.entries(categoryCount).map(([category, count]) => ({
        id: category,
        name: formatCategoryName(category),
        slug: category.toLowerCase().replace(/\s+/g, '-'),
        count: count,
        isSpecial: specialCategories.includes(category.toLowerCase())
      }));

      // Sort by count (descending)
      allCategories.sort((a, b) => b.count - a.count);

      // Separate special categories and regular categories
      const special: Category[] = [];
      const regular: Category[] = [];

      allCategories.forEach(category => {
        if (category.isSpecial) {
          special.push(category);
        } else {
          regular.push(category);
        }
      });

      // Sort special categories in the desired order: hot, trending, new
      special.sort((a, b) => {
        const aIndex = specialCategories.indexOf(a.id.toLowerCase());
        const bIndex = specialCategories.indexOf(b.id.toLowerCase());
        return aIndex - bIndex;
      });

      // If we don't have all special categories from the database, add missing ones
      specialCategories.forEach(specialCat => {
        if (!special.some(cat => cat.id.toLowerCase() === specialCat)) {
          special.push({
            id: specialCat,
            name: formatCategoryName(specialCat),
            slug: specialCat,
            count: 0,
            isSpecial: true
          });
        }
      });

      // Combine special categories first, then regular categories, and limit the total
      const topCategories = [...special, ...regular].slice(0, limit);

      set({ topCategories, isLoading: false });
    } catch (error) {
      // console.error('Error fetching top categories:', error);
      set({
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        isLoading: false
      });
    }
  },

  getCategoryBySlug: (slug: string) => {
    // First check if it's in the categories table
    const dbCategory = get().categories.find(category => category.slug === slug);
    if (dbCategory) {
      return dbCategory;
    }

    // Handle special categories that might not be in the database
    const specialCategories = ['hot', 'trending', 'new'];
    if (specialCategories.includes(slug.toLowerCase())) {
      return {
        id: slug,
        name: formatCategoryName(slug),
        slug: slug.toLowerCase()
      };
    }

    // Check if it's a dynamic category from videos
    const topCategories = get().topCategories;
    return topCategories.find(category => category.slug === slug);
  }
}));

// Helper function to format category names
function formatCategoryName(category: string): string {
  return category
    .split(/[_\s]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};
