import useSWR, { SWRConfiguration } from 'swr';
import { supabase, getVideoQuery } from '../lib/supabase';
import { Video } from '../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../utils/mediaUtils';

// Define the fetcher function
const trendingVideosFetcher = async (key: string) => {
  console.log('🔥 trendingVideosFetcher called with key:', key);

  // Parse the key to extract parameters
  const [_, limit] = key.split('/');
  const limitNum = parseInt(limit) || 8;
  console.log('📊 Parsed limit:', limitNum);

  console.log('📊 Executing trending videos query...');
  const { data, error } = await supabase
    .from('videos')
    .select(`
      id,
      title,
      description,
      thumbnail_url,
      video_url,
      duration,
      views,
      likes,
      is_hd,
      user_id,
      created_at,
      updated_at,
      category,
      tags
    `)
    .order('views', { ascending: false })
    .limit(limitNum);

  console.log('📊 Trending videos query result:', { dataLength: data?.length, error });

  if (error) {
    console.error('❌ Trending videos fetch error:', error);
    throw new Error(error.message);
  }

  // Transform the data to match our Video type and set category to 'trending'
  const trendingVideos: Video[] = data.map(item => ({
    id: item.id,
    title: item.title,
    description: item.description || '',
    thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
    videoUrl: getWorkingVideoUrl(item.video_url || ''),
    duration: item.duration || 0,
    views: item.views || 0,
    likes: item.likes || 0,
    createdAt: item.created_at,
    updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
    publishedAt: item.created_at, // Use created_at since published_at doesn't exist in schema
    scheduledFor: undefined, // Field doesn't exist in schema
    status: 'public', // Field doesn't exist in schema, default to public
    isHD: item.is_hd || false,
    isPremium: false, // Field doesn't exist in schema, default to false
    tags: Array.isArray(item.tags) ? item.tags : [], // Ensure tags is an array
    category: 'trending', // Set category to 'trending' for all trending videos
    originalCategory: item.category || 'uncategorized', // Store original category
    creator: {
      id: item.user_id || '',
      email: '',
      avatar: 'https://via.placeholder.com/150?text=User',
      isVerified: false,
      isCreator: true,
      subscriberCount: 0
    }
  }));

  console.log('🎯 trendingVideosFetcher returning:', { count: trendingVideos.length });
  return trendingVideos;
};

// Define the hook
export function useTrendingVideos(limit: number = 8, config?: SWRConfiguration) {
  const { data, error, isLoading, isValidating, mutate } = useSWR(
    `trending/${limit}`,
    trendingVideosFetcher,
    {
      revalidateOnFocus: false,
      revalidateIfStale: true,
      revalidateOnReconnect: true,
      dedupingInterval: 60000, // 1 minute
      focusThrottleInterval: 120000, // 2 minutes
      ...config
    }
  );

  return {
    trendingVideos: data || [],
    isLoading,
    isValidating,
    error: error?.message,
    mutate
  };
}
