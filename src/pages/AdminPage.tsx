import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { supabase } from '../lib/supabase';
import { Check, X, AlertCircle, UserCheck } from 'lucide-react';
import Button from '../components/ui/Button';

interface UserProfile {
  id: string;
  username: string;
  avatar_url: string | null;
  is_approved: boolean;
  created_at: string;
  email?: string;
}

const AdminPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isLoading } = useAuthStore();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isCheckingAdmin, setIsCheckingAdmin] = useState<boolean>(true);
  const [pendingUsers, setPendingUsers] = useState<UserProfile[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Check if the current user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsCheckingAdmin(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.role === 'admin');
        }
      } catch (err) {
        console.error('Error checking admin status:', err);
        setIsAdmin(false);
      } finally {
        setIsCheckingAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      navigate('/');
    }
  }, [user, isLoading, navigate]);

  // Load pending users
  useEffect(() => {
    const loadPendingUsers = async () => {
      if (!isAdmin) return;

      setIsLoadingUsers(true);
      setError(null);

      try {
        // Get profiles that are not approved
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
          .eq('is_approved', false)
          .order('created_at', { ascending: false });

        if (profilesError) {
          throw profilesError;
        }

        // We can't access user emails directly from the client
        // We'll just use the profiles data without emails
        setPendingUsers(profiles || []);
      } catch (err) {
        console.error('Error loading pending users:', err);
        setError('Failed to load pending users. Please try again.');
      } finally {
        setIsLoadingUsers(false);
      }
    };

    if (isAdmin && !isCheckingAdmin) {
      loadPendingUsers();
    }
  }, [isAdmin, isCheckingAdmin]);

  const approveUser = async (userId: string) => {
    try {
      setError(null);

      const { error } = await supabase
        .from('profiles')
        .update({ is_approved: true })
        .eq('id', userId);

      if (error) throw error;

      // Update the local state
      setPendingUsers(pendingUsers.filter(user => user.id !== userId));
      setSuccessMessage('User approved successfully');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error('Error approving user:', err);
      setError('Failed to approve user. Please try again.');
    }
  };

  if (isLoading || isCheckingAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle size={48} className="text-red-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-400 max-w-md">
              You don't have permission to access the admin area.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl md:text-3xl font-bold mb-6">Admin Dashboard</h1>

      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <UserCheck className="mr-2" /> Pending User Approvals
        </h2>

        {error && (
          <div className="bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="bg-green-900/30 border border-green-500 text-green-300 px-4 py-3 rounded mb-4">
            {successMessage}
          </div>
        )}

        {isLoadingUsers ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : pendingUsers.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No pending users to approve
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="px-4 py-2 text-left">Username</th>
                  <th className="px-4 py-2 text-left">Email</th>
                  <th className="px-4 py-2 text-left">Registered</th>
                  <th className="px-4 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {pendingUsers.map(profile => (
                  <tr key={profile.id} className="border-b border-gray-700">
                    <td className="px-4 py-3">{profile.username}</td>
                    <td className="px-4 py-3">{profile.email || 'N/A'}</td>
                    <td className="px-4 py-3">
                      {new Date(profile.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3 text-right">
                      <Button
                        variant="success"
                        size="sm"
                        leftIcon={<Check size={16} />}
                        onClick={() => approveUser(profile.id)}
                      >
                        Approve
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminPage;
