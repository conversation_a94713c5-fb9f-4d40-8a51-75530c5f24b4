import React from 'react';
import { Video } from '../../types';
import { formatCount, formatDuration } from '../../utils/formatters';
import OptimizedImage from './OptimizedImage';
import { getVideoThumbnailPlaceholder } from '../../utils/imageUtils';

interface VideoCardProps {
  video: Video;
  onClick?: (video: Video) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, onClick }) => {
  const handleClick = () => {
    if (onClick) onClick(video);
  };

  // Check if video is HD
  const isHD = video.isHD;

  return (
    <div
      className="relative overflow-hidden cursor-pointer mb-4"
      onClick={handleClick}
    >
      <div className="relative aspect-video overflow-hidden bg-gray-900">
        <OptimizedImage
          src={video.thumbnailUrl || getVideoThumbnailPlaceholder()}
          alt={video.title}
          fallbackSrc={getVideoThumbnailPlaceholder()}
          className="w-full h-full object-cover"
          width={640}
          height={360}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
        />

        {/* Duration badge */}
        {video.duration > 0 && (
          <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
            {formatDuration(video.duration)}
          </div>
        )}

        {/* HD badge */}
        {isHD && (
          <div className="absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium">
            HD
          </div>
        )}
      </div>

      <div className="py-2">
        <h3 className="text-white font-medium line-clamp-2 text-sm">
          {video.title}
        </h3>
        <div className="flex items-center text-xs text-gray-400 mt-1">
          <span>{formatCount(video.views || 0)} Views</span>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;