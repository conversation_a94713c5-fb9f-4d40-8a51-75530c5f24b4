import React, { useEffect, useState } from 'react';
import { Home, Heart, Tag, HelpCircle, X, Upload, Video, TrendingUp, Flame, Sparkles, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { useCategoryStore } from '../../stores/categoryStore';
import { supabase } from '../../lib/supabase';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SidebarLinkProps {
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  onClick?: () => void;
}

const SidebarLink: React.FC<SidebarLinkProps> = ({ icon, label, isActive = false, onClick }) => {
  return (
    <button
      className={`
        w-full flex items-center px-4 py-2.5 text-sm rounded-lg
        ${isActive ? 'bg-blue-700/20 text-blue-700' : 'text-gray-300 hover:bg-gray-800 hover:text-white'}
        transition-colors duration-200
      `}
      onClick={onClick}
    >
      <span className="flex-shrink-0 mr-3">{icon}</span>
      <span>{label}</span>
    </button>
  );
};

// Get category icon based on category name
const getCategoryIcon = (categoryName: string) => {
  const name = categoryName.toLowerCase();

  if (name === 'hot') return <Flame size={16} className="mr-3 text-blue-700" />;
  if (name === 'trending') return <TrendingUp size={16} className="mr-3 text-blue-500" />;
  if (name === 'new') return <Sparkles size={16} className="mr-3 text-yellow-500" />;

  return <Tag size={16} className="mr-3 text-gray-400" />;
};

// Get text color class for category
const getCategoryTextClass = (categoryName: string) => {
  const name = categoryName.toLowerCase();

  if (name === 'hot') return 'text-blue-700';
  if (name === 'trending') return 'text-blue-400';
  if (name === 'new') return 'text-yellow-400';

  return 'text-gray-300';
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { topCategories, fetchTopCategories, isLoading, error } = useCategoryStore();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);

  // Check if the current user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.role === 'admin');
        }
      } catch (err) {
        console.error('Error checking admin status:', err);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  useEffect(() => {
    fetchTopCategories(6);
  }, [fetchTopCategories]);
  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 h-full w-64 bg-gray-900 border-r border-gray-800 z-40
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}
      >
        {/* Logo and Close Button */}
        <div className="px-4 h-16 flex items-center justify-between">
          <div className="font-bold text-xl text-white">
            <span className="text-blue-500">Blue</span>Film
          </div>
          <button
            className="p-1 rounded-full text-gray-400 hover:text-white hover:bg-gray-800 lg:hidden"
            onClick={onClose}
          >
            <span className="sr-only">Close sidebar</span>
            <X size={20} />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-2 px-2">
          <div className="space-y-1">
            <SidebarLink icon={<Home size={18} />} label="Home" isActive onClick={() => navigate('/', { replace: true })} />
            {user && (
              <>
                <SidebarLink icon={<Upload size={18} />} label="Upload" onClick={() => navigate('/upload')} />
                <SidebarLink icon={<Video size={18} />} label="My Videos" onClick={() => navigate('/manage')} />
                {isAdmin && (
                  <SidebarLink
                    icon={<Shield size={18} />}
                    label="Admin"
                    onClick={() => navigate('/admin')}
                  />
                )}
              </>
            )}
            <SidebarLink icon={<Heart size={18} />} label="Favorites" onClick={() => navigate('/favorites')} />
          </div>

          {/* Top Categories */}
          <div className="mt-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Top Categories
            </h3>
            <div className="mt-2 space-y-1">
              {/* Special Categories: Hot, Trending, New */}
              <div
                className="flex items-center justify-between px-4 py-2 text-sm text-blue-700 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium"
                onClick={() => navigate('/category/hot')}
              >
                <div className="flex items-center">
                  <Flame size={16} className="mr-3 text-blue-700" />
                  <span>Hot</span>
                </div>
              </div>

              <div
                className="flex items-center justify-between px-4 py-2 text-sm text-blue-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium"
                onClick={() => navigate('/category/trending')}
              >
                <div className="flex items-center">
                  <TrendingUp size={16} className="mr-3 text-blue-500" />
                  <span>Trending</span>
                </div>
              </div>

              <div
                className="flex items-center justify-between px-4 py-2 text-sm text-yellow-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium"
                onClick={() => navigate('/category/new')}
              >
                <div className="flex items-center">
                  <Sparkles size={16} className="mr-3 text-yellow-500" />
                  <span>New</span>
                </div>
              </div>
            </div>
          </div>

          {/* Regular Categories - only show if there are non-special categories */}
          {!isLoading && !error && topCategories.filter(category => !['hot', 'trending', 'new'].includes(category.id.toLowerCase())).length > 0 && (
            <div className="mt-4">
              <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Categories
              </h3>
              <div className="mt-2 space-y-1">
                {isLoading ? (
                  <div className="px-4 py-2 text-sm text-gray-500">Loading categories...</div>
                ) : error ? (
                  <div className="px-4 py-2 text-sm text-red-400">Error loading categories</div>
                ) : (
                  topCategories
                    .filter(category => !['hot', 'trending', 'new'].includes(category.id.toLowerCase()))
                    .map((category) => (
                      <div
                        key={category.id}
                        className="flex items-center justify-between px-4 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer"
                        onClick={() => navigate(`/category/${category.slug}`)}
                      >
                        <div className="flex items-center">
                          {getCategoryIcon(category.name)}
                          <span>{category.name}</span>
                        </div>
                        <span className="text-gray-500 text-xs">{category.count || 0}</span>
                      </div>
                    ))
                )}
              </div>
            </div>
          )}


        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="border-t border-gray-800 pt-4">
            <div className="text-xs text-gray-500">
              © 2025 BlueFilm. All rights reserved.
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;